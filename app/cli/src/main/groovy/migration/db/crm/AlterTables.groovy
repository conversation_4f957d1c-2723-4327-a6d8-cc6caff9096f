package migration.db.crm

import lib.data.DBMigrationRunnable
import lib.data.DBMigrationRunnableSet
import lib.data.RunnableReporter
import net.datatp.cli.ShellApplicationContext
import net.datatp.module.data.db.util.DBConnectionUtil
import net.datatp.module.data.db.util.DBUtil

import javax.sql.DataSource
import java.sql.Connection

public class AlterTableSet extends DBMigrationRunnableSet {
    public AlterTableSet() {
        super("""Alter CRM Tables""");

    String label = """Alter CRM Tables""";
    DBMigrationRunnable alterTables = new DBMigrationRunnable(label) {
        @Override
        public void run(RunnableReporter reporter, DBConnectionUtil connUtil) {
          connUtil.execute("""
            ALTER TABLE public.lgc_forwarder_crm_agency_agreement_follow_up ALTER COLUMN agent_code DROP NOT NULL;
          """);
        }
    };
    addRunnable(alterTables);
  }
}



DataSource crmDs = DBUtil.createPostgresDs('datatp-crm', 'datatp-crm', '****************************************************');
Connection conn = crmDs.getConnection();

DBConnectionUtil connUtil = new DBConnectionUtil(conn);

RunnableReporter reporter = new RunnableReporter("dbmigration", "latest")

AlterTableSet migration = new AlterTableSet();
migration.run(reporter, connUtil);

connUtil.close();
return "DONE!!!"