package migration.db.common

import java.sql.Connection

import org.slf4j.Logger

import lib.data.DBMigrationRunnable
import lib.data.DBMigrationRunnableSet
import lib.data.RunnableReporter
import net.datatp.cli.ShellApplicationContext
import net.datatp.module.data.db.util.DBConnectionUtil

public class AlterTableCommonSet extends DBMigrationRunnableSet {
    public AlterTableCommonSet() {
        super("""Alter Common Tables""");

    String label = """Alter Common Tables""";
    DBMigrationRunnable alterTables = new DBMigrationRunnable(label) {
        @Override
        public void run(RunnableReporter reporter, DBConnectionUtil connUtil) {
          connUtil.execute("""
            UPDATE core_wfms_entity_task SET task_type = 'CRM' WHERE entity_ref_type = 'forwarder_sales_daily_task';
          """);

        }
    };
    addRunnable(alterTables);
  }
}

ShellApplicationContext shellContext = (ShellApplicationContext) SHELL_CONTEXT;
Connection conn = shellContext.getPrimaryDBConnection();

DBConnectionUtil connUtil = new DBConnectionUtil(conn);

RunnableReporter reporter = new RunnableReporter("dbmigration", "latest")

AlterTableCommonSet migration = new AlterTableCommonSet();
migration.run(reporter, connUtil);

connUtil.close();
return "DONE!!!"