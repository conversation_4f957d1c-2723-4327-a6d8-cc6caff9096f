package net.datatp.module.document.inv.entity;

import java.io.Serial;
import java.util.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphJoinType;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.module.document.DocumentLogic;
import net.datatp.module.document.entity.*;
import net.datatp.module.document.inv.DocInvoiceLogic;
import net.datatp.module.msa.ie.IEModel;
import net.datatp.module.msa.ie.IEReporter;
import net.datatp.module.msa.ie.ListMapRecord;
import net.datatp.module.msa.ie.MapRecord;
import net.datatp.module.msa.ie.pdf.PDFDocument;
import net.datatp.module.msa.ie.pdf.PDFPage;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.springframework.util.ObjectUtils;

@Entity
@Table(
    name = DocInvoice.TABLE_NAME,
    indexes = {
        @Index(name = DocInvoice.TABLE_NAME + "_document_id", columnList = "document_id"),
        @Index(name = DocInvoice.TABLE_NAME + "_type", columnList = "type"),
    }
)
@DeleteGraphs({
  @DeleteGraph(
    target = DocInvoiceItem.class,
    joinType=DeleteGraphJoinType.OneToMany, joinField = "inv_document_id"
  )
})
@NoArgsConstructor @Getter @Setter
public class DocInvoice extends CompanyEntity implements IDocumentIEEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  //TODO: rename to document_invoice
  public static final String TABLE_NAME = "document_inv_document";

  @Column(name = "document_id")
  private Long documentId;

  @Column(length = 2 * 1024)
  private String description;

  private String type;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "invoice_date")
  private Date invoiceDate;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "receipt_date")
  private Date receiptDate;

  private String symbol;
  private String series;

  @Column(name = "receipt_no")
  private String receiptNo;

  @Column(name = "receipt_serial")
  private String receiptSerial;

  @Column(name = "receipt_number")
  private String receiptNumber;

  @Column(name = "declaration_number")
  private String declarationNumber;

  @Column(name = "lookup_code")
  private String lookupCode;

  @Column(name = "customer_lookup_code")
  private String customerLookupCode;

  @Column(name = "organization_lookup_code")
  private String organizationLookupCode;

  @Column(name = "lookup_website")
  private String lookupWebsite;

  @Embedded
  private DocInvoiceBuyer buyer;

  @Embedded
  private DocInvoiceSeller seller;

  @Embedded
  private DocInvoicePayer payer;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "inv_document_id", referencedColumnName = "id")
  private List<DocInvoiceItem> items;
  
  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "inv_document_id", referencedColumnName = "id")
  private List<DocInvoiceHblDistribution> distributions;

  private String currency;

  private double total;

  @Column(name = "receipt_total")
  private double receiptTotal;

  private double vat;

  @Column(name = "vat_charge")
  private double vatCharge;

  @Column(name = "final_charge")
  private double finalCharge;

  @Column(name = "identity_ie_verification")
  private VerificationMethod identityIEVerification = VerificationMethod.NotVerified;

  @Column(name = "charge_ie_verification")
  private VerificationMethod chargeIEVerification = VerificationMethod.NotVerified;

  @Column(name = "document_ie_verification")
  private VerificationMethod documentIEVerification = VerificationMethod.NotVerified;

  @Enumerated(EnumType.STRING)
  @Column(name = "invoice_ie_procession")
  private ProcessIEMethod invoiceIEProcession = ProcessIEMethod.INCORRECT;

  public DocInvoice(Long documentId) {
    this.documentId = documentId;
  }

  private LinkedHashMap<String, Boolean> getInvProcessBooleanField() {
    LinkedHashMap<String, Boolean> processField = new LinkedHashMap<>();
    processField.put("date", this.getInvoiceDate() == null);
    processField.put("no", StringUtil.isEmpty(this.getSeries()));
    processField.put("serial", StringUtil.isEmpty(this.getSymbol()));
    processField.put("seller:tax_code", StringUtil.isEmpty(this.getSeller().getSellerTaxCode()));
    processField.put("buyer:tax_code", StringUtil.isEmpty(this.getBuyer().getBuyerTaxCode()));
    processField.put("items", this.getItems() == null || this.getItems().isEmpty());
    processField.put("charge:total", this.getTotal() == 0.0);
    processField.put("charge:vat", this.getVatCharge() == 0.0);
    processField.put("charge:final", this.getFinalCharge() == 0.0);
    processField.put("lookup_code", StringUtil.isEmpty(this.getLookupCode()));
    processField.put("lookup_website", StringUtil.isEmpty(this.getLookupWebsite()));
    return processField;
  }

  private LinkedHashMap<String, Boolean> getReceiptProcessBooleanField() {
    LinkedHashMap<String, Boolean> processField = new LinkedHashMap<>();
    processField.put("date", this.getReceiptDate() == null);
    processField.put("receipt_no", StringUtil.isEmpty(this.getReceiptNo()));
    processField.put("receipt_serial", StringUtil.isEmpty(this.getReceiptSerial()));
    processField.put("receipt_number", StringUtil.isEmpty(this.getReceiptNumber()));
    processField.put("declaration_number", StringUtil.isEmpty(this.getDeclarationNumber()));
    processField.put("seller:tax_code", StringUtil.isEmpty(this.getSeller().getSellerTaxCode()));
    processField.put("payer:tax_code", StringUtil.isEmpty(this.getPayer().getPayerTaxCode()));
    processField.put("items", this.getItems() == null || this.getItems().isEmpty());
    processField.put("charge:total", this.getReceiptTotal() == 0.0);
    processField.put("lookup_code", StringUtil.isEmpty(this.getLookupCode()));
    processField.put("lookup_website", StringUtil.isEmpty(this.getLookupWebsite()));
    return processField;
  }

  public String buildMissingField(String type) {
    LinkedHashMap<String, Boolean> processField = new LinkedHashMap<>();

    if (Objects.equals(type, "invoice")) {
      processField = getInvProcessBooleanField();
    }

    if (Objects.equals(type, "receipt")) {
      processField = getReceiptProcessBooleanField();
    }

    List<String> missingFields = new ArrayList<>();
    for (Map.Entry<String, Boolean> entry : processField.entrySet()) {
      if (entry.getValue()) {
        missingFields.add(entry.getKey());
      }
    }

    StringBuilder output = new StringBuilder();

    output.append("[").append(processField.size() - missingFields.size()).append("/").append(processField.size()).append("]");

    if (!missingFields.isEmpty()) {
      output.append("-");
      for (int i = 0; i < missingFields.size(); i++) {
        output.append(missingFields.get(i));
        if (i < missingFields.size() - 1) {
          output.append(", ");
        }
      }
    }

    return output.toString();
  }

  private String buildProcessNote(String type) {
    LinkedHashMap<String, Boolean> processField = new LinkedHashMap<>();

    if (Objects.equals(type, "invoice")) {
      processField = getInvProcessBooleanField();
    }

    if (Objects.equals(type, "receipt")) {
      processField = getReceiptProcessBooleanField();
    }

    List<String> missingFields = new ArrayList<>();
    for (Map.Entry<String, Boolean> entry : processField.entrySet()) {
      if (entry.getValue()) {
        missingFields.add(entry.getKey());
      }
    }

    int totalFields = processField.size();
    int nullCount = 0;

    for (Map.Entry<String, Boolean> entry : processField.entrySet()) {
      if (entry.getValue()) {
        nullCount++;
      }
    }

    ProcessIEMethod result;
    if (nullCount == totalFields) {
      result = ProcessIEMethod.INCORRECT;
    } else if (nullCount == 0) {
      result = ProcessIEMethod.CORRECT;
    } else {
      result = ProcessIEMethod.PARTIAL;
    }

    this.invoiceIEProcession = result;

    StringBuilder output = new StringBuilder();
    output.append("[").append(processField.size() - missingFields.size()).append("/").append(processField.size()).append("]|");
    output.append("[").append(result).append("]");

    if (result == ProcessIEMethod.PARTIAL) {
      if (!missingFields.isEmpty()) {
        output.append("-");
        for (int i = 0; i < missingFields.size(); i++) {
          output.append(missingFields.get(i));
          if (i < missingFields.size() - 1) {
            output.append(", ");
          }
        }
      }
    }
    return output.toString();
  }

	public String getAttrFromDocInvoiceItem(String attrName) {
		return items.isEmpty() ? "" :
				Optional.ofNullable(items.get(0))
						.map(DocInvoiceItem::getAttributes)
						.map(attr -> attr.getString(attrName, ""))
						.orElse("");
	}

  public DocInvoice mapFromVNReceipt(PDFDocument pdfIE, IEReporter reporter, Document document, DocumentLogic logic) {
    PDFPage page = pdfIE.getPage(0);

    IEModel bannerIEModel= page.getSectionIEModel("banner");
    IEModel buyerIEModel = page.getSectionIEModel("buyer");
    IEModel payerIEModel = page.getSectionIEModel("payer");
    IEModel sellerIEModel = page.getSectionIEModel("seller");
    IEModel signatureIEModel = page.getSectionIEModel("signature");

    mapVNReceiptIdentity(bannerIEModel, reporter);
    mapVNReceiptIdentity(sellerIEModel, reporter);
    mapVNReceiptIdentity(payerIEModel, reporter);

    mapDate(bannerIEModel, reporter);
    mapDate(buyerIEModel, reporter);
    mapDate(sellerIEModel, reporter);

    mapLookup(signatureIEModel, reporter);

    seller = new DocInvoiceSeller();
    seller.mapFromVNInvoice(sellerIEModel, reporter, logic);

    payer = new DocInvoicePayer();
    payer.mapFromVNReceipt(payerIEModel, reporter, logic);

    if(items == null) {
      items = new ArrayList<>();
    }
    IEModel itemsIEModel = page.getSectionTableIEModel("items");
    ListMapRecord records = itemsIEModel.fieldAsListMapRecords("records");

    for(MapRecord record : records) {
      DocInvoiceItem item = new DocInvoiceItem();

      MapRecord rowRec = new MapRecord();
      for (MapRecord itemRecord : (ListMapRecord) record.get("rows")) {
        rowRec.putAll(itemRecord);
      }

      MapRecord ieRec = new MapRecord();
      for (MapRecord itemRecord : (ListMapRecord) record.get("ie")) {
        ieRec.putAll(itemRecord);
      }

      item.mapFromIEModel(rowRec);
      item.setAttributes(ieRec);
      items.add(item);
      reporter.collectStat("items", 1);
    }

    IEModel chargeIEModel = page.getSectionIEModel("charge");
    mapVNReceiptCharge(chargeIEModel, itemsIEModel, reporter);

    String processNote = buildProcessNote("receipt");
    String missingField = buildMissingField("receipt");

    document.setProcessNote(processNote);

    String selfMissingField = document.getMissingField();
    if (StringUtil.isBlank(selfMissingField)) {
      document.setMissingField(missingField);
    } else {
      if (!selfMissingField.equals(missingField)) {
        document.setMissingField(missingField);
      }
    }

    return this;
  }

  public DocInvoice mapFromVNInvoice(PDFDocument pdfIE, IEReporter reporter, Document document, DocumentLogic logic) {
    PDFPage page = pdfIE.getPage(0);

    IEModel bannerIEModel= page.getSectionIEModel("banner");
    IEModel buyerIEModel = page.getSectionIEModel("buyer");
    IEModel sellerIEModel = page.getSectionIEModel("seller");
    IEModel footerIEModel = page.getSectionIEModel("footer");

    mapVNInvoiceIdentity(bannerIEModel, reporter);
    mapVNInvoiceIdentity(buyerIEModel, reporter);
    mapVNInvoiceIdentity(sellerIEModel, reporter);

    mapDate(bannerIEModel, reporter);
    mapDate(buyerIEModel, reporter);
    mapDate(sellerIEModel, reporter);

    mapLookup(footerIEModel, reporter);

    buyer = new DocInvoiceBuyer();
    buyer.mapFromVNInvoice(buyerIEModel, reporter, logic);

    seller = new DocInvoiceSeller();
    seller.mapFromVNInvoice(sellerIEModel, reporter, logic);

    if(items == null) {
      items = new ArrayList<>();
    }
    IEModel itemsIEModel = page.getSectionTableIEModel("items");
    ListMapRecord records = itemsIEModel.fieldAsListMapRecords("records");

    for(MapRecord record : records) {
      DocInvoiceItem item = new DocInvoiceItem();

      MapRecord rowRec = new MapRecord();
      for (MapRecord itemRecord : (ListMapRecord) record.get("rows")) {
        rowRec.putAll(itemRecord);
      }

      MapRecord ieRec = new MapRecord();
      for (MapRecord itemRecord : (ListMapRecord) record.get("ie")) {
        ieRec.putAll(itemRecord);
      }

      item.mapFromIEModel(rowRec);
      item.setAttributes(ieRec);
      items.add(item);
      reporter.collectStat("items", 1);
    }

    IEModel chargeIEModel = page.getSectionIEModel("charge");
    mapVNInvoiceCharge(chargeIEModel, itemsIEModel, reporter);

    String processNote = buildProcessNote("invoice");
    String missingField = buildMissingField("invoice");

    document.setProcessNote(processNote);

    String selfMissingField = document.getMissingField();
    if (StringUtil.isBlank(selfMissingField)) {
      document.setMissingField(missingField);
    } else {
      if (!selfMissingField.equals(missingField)) {
        document.setMissingField(missingField);
      }
    }

    return this;
  }


  private void mapDate(IEModel ie, IEReporter reporter) {
//    if (this.invoiceDate != null) return;
    int day = ie.getInteger("vn-day", 0);
    int month = ie.getInteger("vn-month", 0);
    int year = ie.getInteger("vn-year", 0);

    if (day != 0 && month != 0 && year != 0) {
      Calendar calendar = Calendar.getInstance();

      calendar.set(year, month - 1, day, 0, 0, 0);
      calendar.set(Calendar.MILLISECOND, 0);

      Date time = calendar.getTime();

      this.invoiceDate = time;
      this.receiptDate = time;
    }
  }

  private void mapLookup(IEModel ie, IEReporter reporter) {
    if(lookupCode == null) {
      lookupCode = ie.getString("vn-lookup-code");
      reporter.collectStat("vn-lookup-code", lookupCode);
    };

    if(customerLookupCode == null) {
      customerLookupCode = ie.getString("vn-customer-lookup-code");
      reporter.collectStat("vn-customer-lookup-code", customerLookupCode);
    };

    if(organizationLookupCode == null) {
      organizationLookupCode = ie.getString("vn-organization-code");
      reporter.collectStat("vn-organization-code", organizationLookupCode);
    };

    if(lookupWebsite == null) {
      lookupWebsite = ie.getString("vn-lookup-website");
      reporter.collectStat("vn-lookup-website", lookupWebsite);
    };
  }

  private void mapVNReceiptIdentity(IEModel ie, IEReporter reporter) {
    if(receiptNo == null) {
      receiptNo = ie.getString("vn-receipt-no");
      reporter.collectStat("receiptNo", receiptNo);
    };
    if(receiptSerial == null) {
      receiptSerial = ie.getString("vn-receipt-serial");
      reporter.collectStat("receiptSerial", receiptSerial);
    };
    if(receiptNumber == null) {
      receiptNumber = ie.getString("vn-receipt-number");
      reporter.collectStat("receiptNumber", receiptNumber);
    };
    if(declarationNumber == null) {
      declarationNumber = ie.getString("vn-declaration-number");
      reporter.collectStat("declarationNumber", declarationNumber);
    };
  }

  private void mapVNInvoiceIdentity(IEModel ie, IEReporter reporter) {
    if(symbol == null) {
      symbol = ie.getString("vn-inv-serial");
      reporter.collectStat("symbol", symbol);
    };
    if (series == null) {
      series = ie.getString("vn-inv-number");
      reporter.collectStat("series", series);
    }
  }

  private void mapVNInvoiceCharge(IEModel charge_ie, IEModel item_ie, IEReporter reporter) {
    total       = charge_ie.getRealNumber("vn-inv-total", 0d);
    vatCharge   = charge_ie.getRealNumber("vn-inv-vat-charge", 0d);
    finalCharge = charge_ie.getRealNumber("vn-inv-final-charge", 0d);

    MapObject summary = item_ie.getMapObject("summary");

    if (summary != null) {
	    total       = summary.getRealNumber("vn-inv-total", 0d);
	    vatCharge   = summary.getRealNumber("vn-inv-vat-charge", 0d);
	    finalCharge = summary.getRealNumber("vn-inv-final-charge", 0d);

//      String taxPrice = summary.getString("sum:tax-price", "");
//      String totalPrice = summary.getString("sum:total", "");
//
//      if (!taxPrice.isEmpty()) {
//        vatCharge = Double.parseDouble(taxPrice.replaceAll("\\.", ""));
//      }
//
//      if (!totalPrice.isEmpty()) {
//        finalCharge = Double.parseDouble(totalPrice.replaceAll("\\.", ""));
//      }
//
//      if (!taxPrice.isEmpty() && !totalPrice.isEmpty()) {
//        total = finalCharge - vatCharge;
//      }
    }

    reporter.collectStat("total",       total > 0);
    reporter.collectStat("vatCharge",   vatCharge > 0);
    reporter.collectStat("finalCharge", finalCharge > 0);
  }

  private void mapVNReceiptCharge(IEModel charge_ie, IEModel item_ie, IEReporter reporter) {
    receiptTotal       = charge_ie.getRealNumber("vn-receipt-total", 0d);

    MapObject summary = item_ie.getMapObject("summary");

    if (summary != null) {
      String totalPrice = summary.getString("sum:total", "");

      if (!totalPrice.isEmpty()) {
        receiptTotal = Double.parseDouble(totalPrice.replaceAll("\\.", ""));
      }
    }

    reporter.collectStat("total",       total > 0);
  }

  public void set(ClientContext client, ICompany company) {
    super.set(client, company);
    set(client, company, items);
    set(client, company, distributions);
  }
}