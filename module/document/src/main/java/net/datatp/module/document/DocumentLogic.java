package net.datatp.module.document;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import net.datatp.module.document.entity.DocumentPartnerData;
import net.datatp.module.document.repo.DocumentPartnerDataRepo;
import net.datatp.module.msa.MSAClient;
import net.datatp.module.msa.MSAClientService;
import net.datatp.util.ds.MapObject;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.app.AppEnv;
import net.datatp.module.backend.Notification;
import net.datatp.module.core.security.AuthorizationCipherTool;
import net.datatp.module.core.security.SessionData;
import net.datatp.module.data.db.DocumentDAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.document.entity.Document;
import net.datatp.module.document.entity.DocumentSetTag;
import net.datatp.module.document.entity.DocumentTask;
import net.datatp.module.document.inv.DocInvoiceLogic;
import net.datatp.module.document.repo.DocumentRepo;
import net.datatp.module.document.repo.DocumentSetTagRepo;
import net.datatp.module.document.repo.DocumentTaskRepo;
import net.datatp.module.http.get.GETTmpStoreHandler;
import net.datatp.module.http.get.StoreInfo;
import net.datatp.module.http.upload.UploadResource;
import net.datatp.module.http.upload.UploadService;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.module.storage.CompanyStorage;
import net.datatp.module.storage.IStorageService;
import net.datatp.security.client.ClientContext;

@Slf4j
@Component
public class DocumentLogic extends DocumentDAOService {
  @Autowired
  private AppEnv appEnv;

	static String API_URL = "https://msa.datatp.cloud/api";
//  static String API_URL = "http://localhost:8888/api";

	@Autowired
	private DocumentPartnerDataRepo documentPartnerDataRepo;

//  @Autowired
//  DocumentSetRepo documentSetRepo;

  @Autowired
  public DocumentRepo documentRepo;

  @Autowired
  DocumentSetTagRepo setTagRepo;

  @Autowired
  DocumentTaskRepo taskRepo;
  
//  @Autowired
//  DocumentSetDocumentRelationRepo relationRepo;

  @Autowired
  UploadService uploadService;

  @Autowired
  IStorageService storageService;

  @Autowired
  ExecutableUnitManager executableUnitManager;

  @Autowired
  DocInvoiceLogic docInvoiceLogic;

  @Autowired
  private AuthorizationCipherTool cipherTool;

  @Autowired
  GETTmpStoreHandler tmpStoreHandler;
  
//  //Relation
//  public DocumentSetDocumentRelation getDocumentSetDocumentRelation(ClientContext client, ICompany company, DocumentSetDocumentRelation relation) {
//    return relationRepo.save(relation);
//  }
  
//  public DocumentSetDocumentRelation saveDocumentSetDocumentRelation(ClientContext client, ICompany company, DocumentSetDocumentRelation relation) {
//    relation.set(client, company);
//    return relationRepo.save(relation);
//  }
  
//  public List<DocumentSetDocumentRelation> saveDocumentSetDocumentRelations(ClientContext client, ICompany company, List<DocumentSetDocumentRelation> relations) {
//    for(DocumentSetDocumentRelation relation : relations) {
//      relation.set(client, company);
//    }
//    return relationRepo.saveAll(relations);
//  }


  public DocumentSetTag getDocumentSetTag(ClientContext client, ICompany company, Long id) {
    return setTagRepo.getById(company.getId(), id);
  }


  public DocumentSetTag saveDocumentSetTag(ClientContext client, ICompany company, DocumentSetTag tag) {
    tag.set(client, company.getId());
    return setTagRepo.save(tag);
  }

  public List<SqlMapRecord> searchDocumentSetCategories(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir  = getScriptDir();
    String scriptFile = getScriptFile("DocumentSql.groovy");
    sqlParams.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SearchDocumentSetCategory", sqlParams);
  }

  public List<SqlMapRecord> searchDocumentSetTags(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir  = getScriptDir();
    String scriptFile = getScriptFile("DocumentSql.groovy");
    sqlParams.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SearchDocumentSetTag", sqlParams);
  }

//  // DocumentSet
//  public DocumentSet getDocumentSet(ClientContext client, ICompany company, Long id) {
//    return documentSetRepo.getById(company.getId(), id);
//  }
//
//  public DocumentSet saveDocumentSet(ClientContext client, ICompany company, DocumentSet documentSet) {
//    DocumentSetCategory category = getDocumentSetCategory(client, company, documentSet.getDocumentCategoryId());
//    //Objects.assertNotNull(category, "Document Set Category is not found by id = {}", documentSet.getDocumentCategoryId());
//    documentSet.set(client, company.getId());
//    documentSet = documentSetRepo.save(documentSet);
//    return documentSetRepo.save(documentSet);
//  }

//  public List<SqlMapRecord> searchDocumentSets(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
//    String scriptDir  = getScriptDir();
//    String scriptFile = getScriptFile("DocumentSql.groovy");
//    sqlParams.addParam("companyId", company.getId());
//    return searchDbRecords(client, scriptDir, scriptFile, "SearchDocumentSet", sqlParams);
//  }
//
//  public Notification deleteDocumentSets(ClientContext client, ICompany company, List<Long> docSetIds) {
//    String scriptDir  = getScriptDir();
//    ExecutableContext ctx       = new ExecutableContext(client, company)
//        .withScriptEnv(scriptDir, DocumentSetUnitExecutor.class, DocumentSetUnitExecutor.DeleteDocumentSet.class)
//        .withParam("documentSetIds", docSetIds);
//    return (Notification) executableUnitManager.execute(ctx);
//  }

  // Document

  public Document getDocument(ClientContext client, ICompany company, Long id) {
    Document document = documentRepo.getById(company.getId(), id);
    String   docPath  = document.getPath();
    
    SessionData sessionData = new SessionData(client.getToken(), docPath);
    document.setStoreId(cipherTool.encryptSessionData(sessionData));
    return document;
  }

  public List<Document> findDocumentByIds(ClientContext client, ICompany company, List<Long> ids) {
    List<Document> documents = documentRepo.findByIds(company.getId(), ids);
    return documents;
  }

  
  public Document saveDocument(ClientContext client, ICompany company, Document doc) {
    doc.set(client, company);
    return documentRepo.save(doc);
  }
  
  public Document saveAndFlushDocument(ClientContext client, ICompany company, Document doc) {
    doc.set(client, company);
    return documentRepo.saveAndFlush(doc);
  }
  
  public boolean updateDocumentTypes(ClientContext client, ICompany company, List<Long> docIds, String docType) {
    documentRepo.updateDocumentTypes(company.getId(), docIds, docType);
    return true;
  }


	public DocumentPartnerData getDocumentPartnerData(String taxCode) {
		DocumentPartnerData documentPartner  = documentPartnerDataRepo.getByTaxCode(taxCode);

		if (documentPartner != null) {
			return documentPartner;
		} else {
			documentPartner             = new DocumentPartnerData();

			MSAClientService msaService = new MSAClientService();
			msaService.setMsaApiUrl(API_URL);
			MSAClient masClient         = msaService.createMSAClient();

			MapObject userParams        = new MapObject();
			userParams.set("taxCode", taxCode);

			MapObject data = masClient.call("PDFDocumentIEService", "taxcode_data", userParams, MapObject.class);
			String resTaxCode = data.getString("id");

			if (resTaxCode != null) {
				documentPartner.setTaxCode(data.getString("id"));
				documentPartner.setName(data.getString("name"));
				documentPartner.setInternationalName(data.getString("internationalName"));
				documentPartner.setShortName(data.getString("shortName"));
				documentPartner.setAddress(data.getString("address"));

			} else {
				documentPartner.setTaxCode(taxCode);
			}
			return documentPartnerDataRepo.save(documentPartner);
		}
	}

  public Notification uploadDocuments(ClientContext client, ICompany company, Long documentSetId, List<UploadResource> uploadResources) {
    String scriptDir  = getScriptDir();
    ExecutableContext ctx       = new ExecutableContext(client, company)
        .withScriptEnv(scriptDir, DocumentUnitExecutor.class, DocumentUnitExecutor.UploadDocument.class)
        .withParam("documentSetId", documentSetId).withParam("uploadResources", uploadResources);
    return (Notification) executableUnitManager.execute(ctx);
  }

  public Document bulkSaveDocument(ClientContext client, ICompany company, Document doc) {
    Document document = documentRepo.getById(company.getId(), doc.getId());
    document.merge(doc);
    return saveDocument(client, company, document);
  }
  
  public List<Document> bulkSaveDocuments(ClientContext client, ICompany company, List<Document> docs) {
    List<Document> result = new ArrayList<>();
    for (Document doc : docs) {
      Document document = bulkSaveDocument(client, company, doc);
      result.add(document);
    }
    return result;
  }

  public Notification deleteDocuments(ClientContext client, ICompany company, List<Long> docIds) {
    String            scriptDir = appEnv.addonPath("core", "groovy");
    ExecutableContext ctx       = new ExecutableContext(client, company)
        .withScriptEnv(scriptDir, DocumentUnitExecutor.class, DocumentUnitExecutor.DeleteDocument.class)
        .withParam("docIds", docIds);
    return (Notification) executableUnitManager.execute(ctx);
  }

  public List<SqlMapRecord> searchDocuments(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String            scriptDir = appEnv.addonPath("core", "groovy");

    ExecutableContext ctx       = new ExecutableContext(client, company)
        .withScriptEnv(scriptDir, DocumentUnitExecutor.class, DocumentUnitExecutor.SearchDocument.class)
        .withParam("sqlParams", sqlParams);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  public StoreInfo mergePdfDocuments(ClientContext client, ICompany company, List<Long> documentIds) throws IOException {
    String scriptDir  = getScriptDir();
    ExecutableContext ctx       = 
        new ExecutableContext(client, company)
        .withScriptEnv(scriptDir, DocumentUnitExecutor.class, DocumentUnitExecutor.MergePdfDocuments.class)
        .withParam("documentIds", documentIds);
    return (StoreInfo) executableUnitManager.execute(ctx);
  }

  public StoreInfo downloadDocuments(ClientContext client, ICompany company, List<Long> documentIds, boolean keepFileName) {
    String            scriptDir = appEnv.addonPath("core", "groovy");
    ExecutableContext ctx       = 
        new ExecutableContext(client, company)
        .withScriptEnv(scriptDir, DocumentUnitExecutor.class, DocumentUnitExecutor.DownloadDocuments.class)
        .withParam("documentIds", documentIds);
    return (StoreInfo) executableUnitManager.execute(ctx);
  }

  // DocumentTask
  public DocumentTask saveDocumentTask(ClientContext client, ICompany company, DocumentTask task) {
    task.set(client, company.getId());
    return taskRepo.save(task);
  }

  public DocumentTask geDocumentTask(ClientContext client, ICompany company, Long id) {
    return taskRepo.getById(company.getId(), id);
  }

  public List<DocumentTask> findDocumentTasksByDocument(ClientContext client, ICompany company, Long documentId) {
    return taskRepo.findByDocument(company.getId(), documentId);
  }

  public CompanyStorage createCompanyStorage(ClientContext client, ICompany company) {
    return storageService.createCompanyStorage(client, company.getCode());
  }

  String getScriptFile(String name) {
    return "net/datatp/module/document/groovy/" + name;
  }
  
  String getScriptDir() {
    return appEnv.addonPath("core", "groovy");
  }
  
  
  Logger getLogger() { return log; }
}