package net.datatp.module.document.inv.entity;

import java.io.Serial;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.Locale;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.ICompanyEntity;
import net.datatp.module.data.db.entity.Persistable;
import net.datatp.module.msa.ie.MapRecord;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.StringUtil;

@Entity
@Table(name = DocInvoiceItem.TABLE_NAME)
@NoArgsConstructor @Getter @Setter
public class DocInvoiceItem extends Persistable<Long> implements ICompanyEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  //TODO: rename to document_invoice_item
  public static final String TABLE_NAME = "document_inv_item";

  @NotNull
  @Column(name = "company_id")
  @Min(1L)
  private Long companyId;

  @Column(length = 2 * 1024)
  private String name;

  private String symbol;

  private String unit;

  private double qty;

  @Column(name = "unit_price")
  private double unitPrice;

  private double total;

  @Transient
  @JsonIgnore
  private MapObject attributes = new MapObject();

  public Long companyId() {
    return this.companyId;
  }

  public void withCompanyId(Long id) {
    this.companyId = id;
  }

  @Access(AccessType.PROPERTY)
  @Column(name="attributes_json", length = 32 * 1024)
  public String getAttributesJson() {
    if(this.attributes == null || Collections.isEmpty(this.attributes.values())) return null;
    return DataSerializer.JSON.toString(this.attributes);
  }

  public void setAttributesJson(String json) {
    if(StringUtil.isEmpty(json)) {
      this.attributes = null;
    } else {
      this.attributes = DataSerializer.JSON.fromString(json, MapObject.class);
    }
  }

	private static double parseSafeDouble(NumberFormat nf, String value) {
		try {
			return nf.parse(value).doubleValue();
		} catch (ParseException ex) {
			return 0.0;
		}
	}

  public DocInvoiceItem mapFromIEModel(MapRecord itemIEModel) {
	  NumberFormat nf = NumberFormat.getInstance(new Locale("vi", "VN"));

    name      = itemIEModel.getString("name", "");
    unit      = itemIEModel.getString("unit", "");

	  qty       = itemIEModel.getRealNumber("qty", 0d);
	  unitPrice = itemIEModel.getRealNumber("unit-price", 0d);
	  total     = itemIEModel.getRealNumber("total", 0d);

    return this;
  }

  public void set(ClientContext client, Long companyId) {
    super.set(client);
    this.companyId = companyId;
  }
}