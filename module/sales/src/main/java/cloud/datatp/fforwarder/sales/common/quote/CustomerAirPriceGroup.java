package cloud.datatp.fforwarder.sales.common.quote;

import cloud.datatp.fforwarder.price.common.AirPriceGroup;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.util.ds.MapObject;

@Embeddable
@Getter
@Setter
@NoArgsConstructor
public class CustomerAirPriceGroup {

  @Column(name = "selected_price_type")
  private String selectedPriceType;

  @Column(name = "selected_price")
  private double selectedPrice;

  /*-------------- SELLING --------------*/
  @Column(name = "min_price")
  private double minPrice;

  @Column(name = "level1_price")
  private double level1Price;

  @Column(name = "level2_price")
  private double level2Price;

  @Column(name = "level3_price")
  private double level3Price;

  @Column(name = "level4_price")
  private double level4Price;

  @Column(name = "level5_price")
  private double level5Price;

  @Column(name = "level6_price")
  private double level6Price;

  @Column(name = "level7_price")
  private double level7Price;
  /*-------------- SELLING --------------*/

  /*-------------- REFERENCE (Costing) --------------*/
  @Column(name = "ref_min_price")
  protected double refMinPrice;

  @Column(name = "ref_level_1_price")
  protected double refLevel1Price;

  @Column(name = "ref_level_2_price")
  protected double refLevel2Price;

  @Column(name = "ref_level_3_price")
  protected double refLevel3Price;

  @Column(name = "ref_level_4_price")
  protected double refLevel4Price;

  @Column(name = "ref_level_5_price")
  protected double refLevel5Price;

  @Column(name = "ref_level_6_price")
  protected double refLevel6Price;

  @Column(name = "ref_level_7_price")
  protected double refLevel7Price;

  @Column(name = "ref_min_price_note")
  private String refMinPriceNote;

  @Column(name = "ref_level1_price_note")
  private String refLevel1PriceNote;

  @Column(name = "ref_level2_price_note")
  private String refLevel2PriceNote;

  @Column(name = "ref_level3_price_note")
  private String refLevel3PriceNote;

  @Column(name = "ref_level4_price_note")
  private String refLevel4PriceNote;

  @Column(name = "ref_level5_price_note")
  private String refLevel5PriceNote;

  @Column(name = "ref_level6_price_note")
  private String refLevel6PriceNote;

  @Column(name = "ref_level7_price_note")
  private String refLevel7PriceNote;

  /*-------------- REFERENCE (Costing) --------------*/

  public CustomerAirPriceGroup(AirPriceGroup priceGroup) {
    if (priceGroup == null) return;
    this.minPrice = priceGroup.getMinPrice();
    this.level1Price = priceGroup.getLevel1Price();
    this.level2Price = priceGroup.getLevel2Price();
    this.level3Price = priceGroup.getLevel3Price();
    this.level4Price = priceGroup.getLevel4Price();
    this.level5Price = priceGroup.getLevel5Price();
    this.level6Price = priceGroup.getLevel6Price();
    this.level7Price = priceGroup.getLevel7Price();

    this.refMinPrice = priceGroup.getMinPrice();
    this.refLevel1Price = priceGroup.getLevel1Price();
    this.refLevel2Price = priceGroup.getLevel2Price();
    this.refLevel3Price = priceGroup.getLevel3Price();
    this.refLevel4Price = priceGroup.getLevel4Price();
    this.refLevel5Price = priceGroup.getLevel5Price();
    this.refLevel6Price = priceGroup.getLevel6Price();
    this.refLevel7Price = priceGroup.getLevel7Price();

    this.refMinPriceNote = priceGroup.getMinPriceNote();
    this.refLevel1PriceNote = priceGroup.getLevel1PriceNote();
    this.refLevel2PriceNote = priceGroup.getLevel2PriceNote();
    this.refLevel3PriceNote = priceGroup.getLevel3PriceNote();
    this.refLevel4PriceNote = priceGroup.getLevel4PriceNote();
    this.refLevel5PriceNote = priceGroup.getLevel5PriceNote();
    this.refLevel6PriceNote = priceGroup.getLevel6PriceNote();
    this.refLevel7PriceNote = priceGroup.getLevel7PriceNote();
  }

  public CustomerAirPriceGroup(AirPriceGroup priceGroup, double changeableWeight) {
    this(priceGroup);
    this.selectedPrice = computePriceValue(changeableWeight);
    this.selectedPriceType = computePriceType(changeableWeight);
  }

  // sea charge to sea quote
  public MapObject toMapObject() {
    final MapObject airFreightMap = new MapObject();
    airFreightMap.put("selectedPrice", this.selectedPrice);
    airFreightMap.put("selectedPriceType", this.selectedPriceType);

    airFreightMap.put("refMinPrice", getRefMinPrice());
    airFreightMap.put("refLevel1Price", getRefLevel1Price());
    airFreightMap.put("refLevel2Price", getRefLevel2Price());
    airFreightMap.put("refLevel3Price", getRefLevel3Price());
    airFreightMap.put("refLevel4Price", getRefLevel4Price());
    airFreightMap.put("refLevel5Price", getRefLevel5Price());
    airFreightMap.put("refLevel6Price", getRefLevel6Price());
    airFreightMap.put("refLevel7Price", getRefLevel7Price());

    airFreightMap.put("refMinPriceNote", getRefMinPriceNote());
    airFreightMap.put("refLevel1PriceNote", getRefLevel1PriceNote());
    airFreightMap.put("refLevel2PriceNote", getRefLevel2PriceNote());
    airFreightMap.put("refLevel3PriceNote", getRefLevel3PriceNote());
    airFreightMap.put("refLevel4PriceNote", getRefLevel4PriceNote());
    airFreightMap.put("refLevel5PriceNote", getRefLevel5PriceNote());
    airFreightMap.put("refLevel6PriceNote", getRefLevel6PriceNote());
    airFreightMap.put("refLevel7PriceNote", getRefLevel7PriceNote());

    return airFreightMap;
  }

  public double computePriceValue(double chargeableWeight) {
    if (selectedPrice != 0) return selectedPrice;
    double value = 0;
    if (chargeableWeight <= 10) {
      value = minPrice;
    } else if (chargeableWeight < 45) {
      value = level1Price;
    } else if (chargeableWeight < 100) {
      value = level2Price;
    } else if (chargeableWeight < 250) {
      value = level3Price;
    } else if (chargeableWeight < 300) {
      value = level4Price;
    } else if (chargeableWeight < 500) {
      value = level5Price;
    } else if (chargeableWeight < 1000) {
      value = level6Price;
    } else {
      value = level7Price;
    }
    this.selectedPrice = value;
    return value;
  }

  public String computePriceType(double chargeableWeight) {
    if (chargeableWeight <= 10) {
      this.selectedPriceType = "minPrice";
    } else if (chargeableWeight < 45) {
      this.selectedPriceType = "level1Price";
    } else if (chargeableWeight < 100) {
      this.selectedPriceType = "level2Price";
    } else if (chargeableWeight < 250) {
      this.selectedPriceType = "level3Price";
    } else if (chargeableWeight < 300) {
      this.selectedPriceType = "level4Price";
    } else if (chargeableWeight < 500) {
      this.selectedPriceType = "level5Price";
    } else if (chargeableWeight < 1000) {
      this.selectedPriceType = "level6Price";
    } else {
      this.selectedPriceType = "level7Price";
    }
    return this.selectedPriceType;
  }

}