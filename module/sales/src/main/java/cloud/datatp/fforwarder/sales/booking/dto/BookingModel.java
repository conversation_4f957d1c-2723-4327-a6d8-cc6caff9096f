package cloud.datatp.fforwarder.sales.booking.dto;

import cloud.datatp.fforwarder.core.common.ChargeType;
import cloud.datatp.fforwarder.core.common.FreightTerm;
import cloud.datatp.fforwarder.core.common.Purpose;
import cloud.datatp.fforwarder.core.common.TransportationMode;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.price.common.ChargeTarget;
import cloud.datatp.fforwarder.price.common.ClientPartnerType;
import cloud.datatp.fforwarder.price.entity.CustomClearanceType;
import cloud.datatp.fforwarder.sales.booking.dto.SellingRate.Type;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import cloud.datatp.fforwarder.sales.booking.entity.BookingAirTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingCustomClearance;
import cloud.datatp.fforwarder.sales.booking.entity.BookingSeaTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingTruckTransportCharge;
import cloud.datatp.fforwarder.sales.common.BookingShipmentInfo;
import cloud.datatp.fforwarder.sales.common.ContainerType;
import cloud.datatp.fforwarder.sales.common.ContainerType.ContainerTypeUnit;
import cloud.datatp.fforwarder.sales.common.entity.CustomerAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerAirTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerCustomClearanceAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerSeaTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerSeaTransportCharge.SeaType;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportCharge.TruckType;
import cloud.datatp.fforwarder.sales.common.entity.QuotationCharge;
import cloud.datatp.fforwarder.sales.inquiry.entity.Container;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@NoArgsConstructor
@Getter @Setter
public class BookingModel {

  private Long    id;                     // booking entity id
  private String  referenceNo;            // bfsone reference no.
  private Long sQuotationChargeId;        // quotation charge id

  private String  bookingNo;

  /* ------------ Sender (Saleman) / Receiver (Cus/ Docs) -------------- */
  private Long    senderAccountId;
  private String  senderLabel;

  private Long    receiverAccountId;
  private String  receiverEmployeeLabel;
  private String  receiverBFSOneCode;

  /* ------------ Port/ Airport/ Location -------------- */
  private String  fromLocationCode;
  private String  fromLocationLabel;
  private String  toLocationCode;
  private String  toLocationLabel;
  private String  cargoPickupAt;
  private String  cargoDeliveryAt;

  private String  shipmentType;

  @Enumerated(EnumType.STRING)
  private FreightTerm paymentTerm;

  private int packages;
  private String unitOfPackage;

  private double grossWeight;
  private double volume;

  private String commodity;
  private String descriptionOfGoods;

  private Long carrierPartnerId;
  private String carrierLabel;

  private String planMBCode;
  private String planHBCode;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date planTimeArrival;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date planTimeDeparture;

  private String bookingNote;

  private SpecificServiceInquiry inquiry;

  @Deprecated
  private QuotationCharge airQuote;

  private QuotationCharge seaQuote;

  private List<SellingRate> sellingRates = new ArrayList<>();

  public BookingModel(Booking booking, SpecificServiceInquiry inquiry) {
    this.inquiry = inquiry;
    this.referenceNo = booking.getBfsoneReference();
    this.sQuotationChargeId = booking.getSQuotationChargeId();
    this.id = booking.getId();
    this.bookingNo = booking.getBookingNumber();

    this.shipmentType = booking.getShipmentType();
    this.paymentTerm = booking.getPaymentTerm();
    this.planHBCode = booking.getHawbNo();
    this.planMBCode = booking.getMawbNo();

    this.receiverAccountId = booking.getReceiverAccountId();
    this.receiverEmployeeLabel = booking.getReceiverLabel();
    this.receiverBFSOneCode = booking.getReceiverBFSOneCode();

    this.senderAccountId = inquiry.getSalemanAccountId();
    this.senderLabel = inquiry.getSalemanLabel();

    this.fromLocationCode = inquiry.getFromLocationCode();
    this.fromLocationLabel = inquiry.getFromLocationLabel();
    this.toLocationCode = inquiry.getToLocationCode();
    this.toLocationLabel = inquiry.getToLocationLabel();

    this.cargoPickupAt = inquiry.getPickupAddress();
    this.cargoDeliveryAt = inquiry.getDeliveryAddress();

    this.unitOfPackage = inquiry.getContainerTypes();
    this.grossWeight = inquiry.getGrossWeightKg();
    this.volume = inquiry.getVolumeCbm();
    this.commodity = inquiry.getCommodity();
    this.descriptionOfGoods = inquiry.getDescOfGoods();
    this.packages = inquiry.getPackageQty();
    this.bookingNote = booking.getNote();
  }

  public BookingModel withSeaCharge(BookingSeaTransportCharge seaCharge) {
    if (seaCharge != null) {
      this.sellingRates = SellingRate.sellingRateCreator(inquiry.getContainers(), seaCharge);
    }
    return this;
  }

  public BookingModel withAirCharge(BookingAirTransportCharge airCharge) {
    if (airCharge != null) {
      this.sellingRates = SellingRate.sellingRateCreator(airCharge);
    }
    return this;
  }

  public BookingModel withTruckCharges(List<BookingTruckTransportCharge> truckCharges) {
    if (Collections.isEmpty(truckCharges)) return this;
    List<CustomerTruckTransportAdditionalCharge> truckAddCharges = truckCharges.stream()
      .flatMap(sel -> sel.getAdditionalCharges().stream())
      .collect(Collectors.toList());
    List<SellingRate> truckSellingFees = SellingRate.sellingRateCreator(inquiry.getMode(), truckAddCharges);
    this.sellingRates.addAll(truckSellingFees);
    return this;
  }

  public BookingModel withCustomClearances(List<BookingCustomClearance> customClearances) {
    if (Collections.isEmpty(customClearances)) return this;
    List<CustomerCustomClearanceAdditionalCharge> customAddCharges = customClearances.stream()
      .flatMap(sel -> sel.getAdditionalCharges().stream())
      .collect(Collectors.toList());
    List<SellingRate> customSellingFees = SellingRate.sellingRateCreator(inquiry.getMode(), customAddCharges);
    this.sellingRates.addAll(customSellingFees);
    return this;
  }

  public Booking toBooking(Booking template) {
    template = Objects.ensureNotNull(template, Booking::new);
    Objects.assertNotNull(this.inquiry, "Inquiry is not found");
    Objects.assertNotNull(this.inquiry.getId(), "Inquiry ID must not be null");

    if (this.inquiry != null) {
      template.setInquiryId(inquiry.getId());
    }
    if (template.isNew()) {
      template.setBookingNumber(bookingNo);
      template.setBookingDate(new Date());
    }

    template.setReceiverAccountId(receiverAccountId);
    template.setReceiverLabel(receiverEmployeeLabel);
    template.setReceiverBFSOneCode(receiverBFSOneCode);

    template.setPaymentTerm(paymentTerm);
    template.setShipmentType(shipmentType);
    template.setHawbNo(planHBCode);
    template.setMawbNo(planMBCode);

    if(sQuotationChargeId != null) {
      template.setSQuotationChargeId(sQuotationChargeId);
    }else if(seaQuote != null) {
      template.setSQuotationChargeId(seaQuote.getId());
    } else if(airQuote != null) {
      template.setSQuotationChargeId(airQuote.getId());
    }

    return template;
  }

  public List<BookingTruckTransportCharge> computeTruckTransportCharges(Booking template) {
    TransportationMode mode = inquiry.getMode();

    if (TransportationMode.isSeaTransport(mode)) template.setChargeType(ChargeType.SEA);
    else if (TransportationMode.isAirTransport(mode)) template.setChargeType(ChargeType.AIR);
    else throw RuntimeError.UnknownError("Not support yet!!!");

    Map<TransportationMode, List<SellingRate>> sellingRateByGroup = sellingRates.stream()
      .collect(Collectors.groupingBy(SellingRate::getGroup));

    List<SellingRate> freightRates = sellingRateByGroup.getOrDefault(mode, new ArrayList<>());
    Map<Type, List<SellingRate>> sellingGroup = freightRates.stream().collect(Collectors.groupingBy(SellingRate::getType));

    List<SellingRate> truckingRates = sellingGroup.getOrDefault(Type.TRUCKING, new ArrayList<>());

    List<BookingTruckTransportCharge> truckCharges = new ArrayList<>();
    if (Collections.isNotEmpty(truckingRates)) {
      Map<ChargeTarget, List<SellingRate>> collect = truckingRates.stream().collect(Collectors.groupingBy(SellingRate::getTarget));
      for (ChargeTarget target : collect.keySet()) {
        List<SellingRate> sellingRates = collect.get(target);
        BookingTruckTransportCharge truckCharge = computeTruckTransportCharge(target, template);
        for (SellingRate rate : sellingRates) {
          String unit = rate.getUnit();
          ContainerType containerType = ContainerTypeUnit.match(unit);

          CustomerTruckTransportAdditionalCharge addCharge = new CustomerTruckTransportAdditionalCharge();
          addCharge.setLabel(rate.getName());
          addCharge.setName(rate.getCode());
          addCharge.setCurrency(rate.getCurrency());
          addCharge.setUnitPrice(rate.getUnitPrice());
          addCharge.setNote(rate.getNote());
          addCharge.setExchangeRate(rate.getExchangeRate());
          if (containerType != null) {
            truckCharge.setTruckType(TruckType.CONTAINER);
            addCharge.setUnit(containerType.getLabel());
          } else {
            truckCharge.setTruckType(TruckType.REGULAR);
            addCharge.setUnit(unit);
          }
          addCharge.setTaxRate(rate.getTaxRate());
          addCharge.setFinalCharge(rate.getTotalAmount());
          addCharge.setFinalCharge(rate.getTotalAmount());
          addCharge.setDomesticCurrency(rate.getDomesticCurrency());
          addCharge.setDomesticFinalCharge(rate.getDomesticTotalAmount());
          truckCharge.withAdditionalCharge(addCharge);

        }

        truckCharges.add(truckCharge);
      }
    }

    return truckCharges;
  }

  private BookingTruckTransportCharge computeTruckTransportCharge(ChargeTarget target, Booking template) {
    BookingTruckTransportCharge charge = new BookingTruckTransportCharge();
    charge.setEditMode(EditMode.VALIDATED);
    charge.setValidFrom(new Date());
    charge.setValidTo(new Date());
    charge.setCurrency("VND");
    TransportationMode mode = inquiry.getMode();
    charge.setTruckType(TruckType.CONTAINER);
    if (TransportationMode.isAirTransport(mode)) charge.setTruckType(TruckType.REGULAR);
    if (TransportationMode.isTruckRegular(mode)) charge.setTruckType(TruckType.REGULAR);

    if (target.equals(ChargeTarget.ORIGIN)) {
      charge.setPickupAddress(inquiry.getPickupAddress());
      charge.setDeliveryLocationCode(inquiry.getFromLocationCode());
      charge.setDeliveryLocationLabel(inquiry.getFromLocationLabel());
    } else {
      charge.setPickupLocationCode(inquiry.getToLocationCode());
      charge.setPickupLocationLabel(inquiry.getToLocationLabel());
      charge.setDeliveryLocationLabel(inquiry.getDeliveryAddress());
    }

    charge.setRoute(charge.getPickupLocationCode() + "-" + charge.getDeliveryLocationCode());
    if (StringUtil.isEmpty(charge.getCarrierLabel())) charge.setCarrierRoute(charge.getRoute() + "-N/A");
    else charge.setCarrierRoute(charge.getRoute() + "-" + charge.getCarrierLabel());
    charge.setAssigneeLabel(inquiry.getSalemanLabel());
    return charge;
  }

  public List<BookingCustomClearance> computeCustomClearances(Booking template) {
    TransportationMode mode = inquiry.getMode();

    if (TransportationMode.isSeaTransport(mode)) template.setChargeType(ChargeType.SEA);
    else if (TransportationMode.isAirTransport(mode)) template.setChargeType(ChargeType.AIR);
    else throw RuntimeError.UnknownError("Not support yet!!!");

    Map<TransportationMode, List<SellingRate>> sellingRateByGroup = sellingRates.stream()
      .collect(Collectors.groupingBy(SellingRate::getGroup));

    List<SellingRate> freightRates = sellingRateByGroup.getOrDefault(mode, new ArrayList<>());
    Map<Type, List<SellingRate>> sellingGroup = freightRates.stream().collect(Collectors.groupingBy(SellingRate::getType));

    List<SellingRate> customRates = sellingGroup.getOrDefault(Type.CUSTOM, new ArrayList<>());
    List<BookingCustomClearance> customCharges = new ArrayList<>();
    if (Collections.isNotEmpty(customCharges)) {
      Map<ChargeTarget, List<SellingRate>> collect = customRates.stream().collect(Collectors.groupingBy(SellingRate::getTarget));
      for (ChargeTarget target : collect.keySet()) {
        List<SellingRate> sellingRates = collect.get(target);
        BookingCustomClearance customClearance =  new BookingCustomClearance();
        customClearance.setType(CustomClearanceType.SEA_FCL);
        customClearance.setValidFrom(new Date());
        customClearance.setValidTo(new Date());
        customClearance.setCurrency("VND");
        customClearance.setAssigneeLabel(inquiry.getSalemanLabel());

        for (SellingRate rate : sellingRates) {
          String unit = rate.getUnit();
          ContainerType containerType = ContainerTypeUnit.match(unit);
          CustomerCustomClearanceAdditionalCharge addCharge = new CustomerCustomClearanceAdditionalCharge();
          addCharge.setLabel(rate.getName());
          addCharge.setName(rate.getCode());
          addCharge.setCurrency(rate.getCurrency());
          addCharge.setUnitPrice(rate.getUnitPrice());
          addCharge.setNote(rate.getNote());
          addCharge.setTaxRate(rate.getTaxRate());
          addCharge.setFinalCharge(rate.getTotalAmount());
          addCharge.setDomesticCurrency(rate.getDomesticCurrency());
          addCharge.setDomesticFinalCharge(rate.getDomesticTotalAmount());

          double total = rate.getQuantity() * rate.getUnitPrice();
          double totalTax = total * rate.getTaxRate();
          addCharge.setTotal(total);
          addCharge.setTotalTax(totalTax);
          addCharge.setFinalCharge(total + totalTax);

          if (containerType != null) {
            addCharge.setUnit(containerType.getLabel());
          } else {
            addCharge.setUnit(unit);
          }
          customClearance.withAdditionalCharge(addCharge);
        }
        customCharges.add(customClearance);
      }
    }
    return customCharges;
  }

  public BookingAirTransportCharge computeAirCharge(BookingAirTransportCharge charge) {
    TransportationMode mode = inquiry.getMode();

    Map<TransportationMode, List<SellingRate>> sellingRateByGroup = sellingRates.stream()
      .collect(Collectors.groupingBy(SellingRate::getGroup));

    List<SellingRate> freightRates = sellingRateByGroup.getOrDefault(mode, new ArrayList<>());
    Map<Type, List<SellingRate>> sellingGroup = freightRates.stream().collect(Collectors.groupingBy(SellingRate::getType));

    List<CustomerAdditionalCharge> addCharges = createAddCharges(sellingGroup.getOrDefault(Type.LOCAL_CHARGE, new ArrayList<>()));

    charge = Objects.ensureNotNull(charge, BookingAirTransportCharge::new);
    final BookingShipmentInfo shipmentInfo = getBookingShipmentInfo();

    charge.setShipmentInfo(shipmentInfo);
    charge.setFromLocationCode(fromLocationCode);
    charge.setFromLocationLabel(fromLocationLabel);
    charge.setToLocationCode(toLocationCode);
    charge.setToLocationLabel(toLocationLabel);

    if(ClientPartnerType.isAgent(inquiry.getClientPartnerType())) {
      charge.setPayerPartnerId(inquiry.getHandlingAgentPartnerId());
      charge.setPayerFullName(inquiry.getHandlingAgentLabel());
    } else {
      charge.setPayerPartnerId(inquiry.getClientPartnerId());
      charge.setPayerFullName(inquiry.getClientLabel());
    }

    if (airQuote != null) {
      charge.setChargeableWeightInKG(grossWeight);
      charge.setPurpose(airQuote.getPurpose());
      charge.setRefCurrency(airQuote.getRefCurrency());
      charge.setCurrency(airQuote.getCurrency());
      charge.setNote(airQuote.getNote());
      charge.setValidFrom(new Date());
      charge.setValidTo(airQuote.getValidity());
    }

    List<SellingRate> rates = sellingGroup.getOrDefault(Type.AIRFREIGHT, new ArrayList<>());
    if (!rates.isEmpty()) {
      Objects.assertTrue(rates.size() == 1, "Only one rate is allowed for Air Freight");
      SellingRate.convertToAirFreight(charge, rates.get(0));
    }
    List<CustomerAirTransportAdditionalCharge> mappedAddCharges = addCharges.stream()
      .map(CustomerAirTransportAdditionalCharge::new)
      .collect(Collectors.toList());
    charge.setTemporaryAdditionalCharges(mappedAddCharges);

    return charge;
  }

  public BookingSeaTransportCharge computeSeaCharge(BookingSeaTransportCharge charge) {
    TransportationMode mode = inquiry.getMode();

    Map<TransportationMode, List<SellingRate>> sellingRateByGroup = sellingRates.stream()
      .collect(Collectors.groupingBy(SellingRate::getGroup));

    List<SellingRate> freightRates = sellingRateByGroup.getOrDefault(mode, new ArrayList<>());
    Map<Type, List<SellingRate>> sellingGroup = freightRates.stream().collect(Collectors.groupingBy(SellingRate::getType));

    List<CustomerAdditionalCharge> addCharges = createAddCharges(sellingGroup.getOrDefault(Type.LOCAL_CHARGE, new ArrayList<>()));

    if (charge == null) {
      charge = new BookingSeaTransportCharge();
      if (TransportationMode.isSeaFCLTransport(mode)) charge.setType(SeaType.FCL);
      else charge.setType(SeaType.LCL);
    }
    BookingShipmentInfo shipmentInfo = getBookingShipmentInfo();
    charge.setShipmentInfo(shipmentInfo);

    charge.setFromLocationCode(fromLocationCode);
    charge.setFromLocationLabel(fromLocationLabel);
    charge.setToLocationCode(toLocationCode);
    charge.setToLocationLabel(toLocationLabel);

    if(ClientPartnerType.isAgent(inquiry.getClientPartnerType())) {
      charge.setPayerPartnerId(inquiry.getHandlingAgentPartnerId());
      charge.setPayerFullName(inquiry.getHandlingAgentLabel());
    } else {
      charge.setPayerPartnerId(inquiry.getClientPartnerId());
      charge.setPayerFullName(inquiry.getClientLabel());
    }

    if (seaQuote != null) {
      charge.setChargeableVolumeInCBM(volume);
      charge.setPurpose(inquiry.getPurpose());
      charge.setRefCurrency(seaQuote.getRefCurrency());
      charge.setCurrency(seaQuote.getCurrency());
      charge.setNote(seaQuote.getNote());
      charge.setValidFrom(new Date());
      charge.setValidTo(seaQuote.getValidity());
    }

    List<SellingRate> oceanFreights = sellingGroup.getOrDefault(Type.SEAFREIGHT, new ArrayList<>());
    if (!oceanFreights.isEmpty()) {
      if (TransportationMode.isSeaFCLTransport(mode)) {
        SellingRate.convertToOceanFreightFCL(charge, oceanFreights);
      } else {
        Objects.assertTrue(oceanFreights.size() == 1, "Only one rate is allowed for LCL");
        SellingRate.convertToOceanFreightLCL(charge, oceanFreights.get(0));
      }
    }

    List<CustomerSeaTransportAdditionalCharge> mappedAddCharges = addCharges.stream()
      .map(CustomerSeaTransportAdditionalCharge::new)
      .collect(Collectors.toList());

    charge.setTemporaryAdditionalCharges(mappedAddCharges);

    return charge;
  }

  private BookingShipmentInfo getBookingShipmentInfo() {
    BookingShipmentInfo shipmentInfo = new BookingShipmentInfo();
    shipmentInfo.setCarrierPartnerId(carrierPartnerId);
    shipmentInfo.setCarrierLabel(carrierLabel);
    shipmentInfo.setPlanHBCode(planHBCode);
    shipmentInfo.setPlanMBCode(planMBCode);
    shipmentInfo.setPlanTimeArrival(planTimeArrival);
    shipmentInfo.setPlanTimeDeparture(planTimeDeparture);
    return shipmentInfo;
  }

  public List<CustomerAdditionalCharge> createAddCharges(List<SellingRate> sellingRates) {
    List<CustomerAdditionalCharge> holder = new ArrayList<>();
    for (SellingRate sel : sellingRates) {
      CustomerAdditionalCharge addCharge = new CustomerAdditionalCharge();
      addCharge.setPayerFullName(sel.getPayerPartnerLabel());
      addCharge.setPayerPartnerId(sel.getPayerPartnerId());
      addCharge.setName(sel.getCode());
      addCharge.setLabel(sel.getName());
      addCharge.setQuantity(sel.getQuantity());
      addCharge.setUnitPrice(sel.getUnitPrice());
      addCharge.setUnit(sel.getUnit());
      addCharge.setCurrency(sel.getCurrency());
      addCharge.setTaxRate(sel.getTaxRate());
      addCharge.setNote(sel.getNote());
      addCharge.setExchangeRate(sel.getExchangeRate());

      double total = sel.getQuantity() * sel.getUnitPrice();
      addCharge.setTotal(total);
      addCharge.setTotalTax(total * sel.getTaxRate());
      addCharge.setFinalCharge(sel.getTotalAmount());
      addCharge.setDomesticFinalCharge(sel.getDomesticTotalAmount());
      holder.add(addCharge);
    }
    return holder;
  }

  public MapObject toBFSOneIBooking(CRMPartner customer) {
    Objects.assertNotNull(customer, "Customer is not found when create Internal Booking!");
    MapObject ibooking = new MapObject();
    if (StringUtil.isEmpty(referenceNo)) ibooking.put("CreatedDate", DateUtil.asBFSOneFormat(new Date()));
    ibooking.set("ServiceType", computeServiceType());
    ibooking.set("BkgID", referenceNo);

    ibooking.set("ReceiveUserID", "");
    ibooking.set("SendUserID", "");

    ibooking.set("CustomerID", customer.getBfsonePartnerCode());
    ibooking.set("ColoaderID", "");
    ibooking.set("AgentID", "");
    ibooking.set("ShipperName", customer.getPrintCustomConfirmBillInfo());
    ibooking.set("ConsigneeID", "");
    ibooking.set("ConsigneeName", customer.getPrintCustomConfirmBillInfo());

    ibooking.set("ShipmentType", shipmentType);
    ibooking.set("POLCode", fromLocationCode);
    ibooking.set("POLName", fromLocationLabel);
    ibooking.set("PODCode", toLocationCode);
    ibooking.set("PODName", toLocationLabel);
    ibooking.set("DeliveryPlace", toLocationLabel);
    ibooking.set("ETA", DateUtil.asBFSOneFormat(planTimeArrival));
    ibooking.set("ETD", DateUtil.asBFSOneFormat(planTimeDeparture));
    ibooking.set("Flight_Vessel", "");
    ibooking.set("Voyage", "");
    ibooking.set("Flight_Vessel_Date", DateUtil.asBFSOneFormat(planTimeArrival));
    ibooking.set("Commodity", commodity);
    ibooking.set("DescriptionOfGoods", descriptionOfGoods);
    ibooking.set("GW", grossWeight);
    ibooking.set("Packages", 0);
    if (TransportationMode.isSeaFCLTransport(inquiry.getMode())) {
      ibooking.set("UnitOfPackage", unitOfPackage);
    } else {
      ibooking.set("UnitOfPackage", unitOfPackage);
    }
    ibooking.set("CBM", volume);
    ibooking.set("CargoPickupAt", cargoPickupAt);
    ibooking.set("CargoDeliveryAt", cargoDeliveryAt);
    ibooking.set("MAWBNO", planMBCode);
    ibooking.set("HAWBNO", planHBCode);
    ibooking.set("BookingNo", bookingNo);

    String safeNote = bookingNote != null && bookingNote.length() > 399
      ? bookingNote.substring(0, 399)
      : bookingNote;

    ibooking.set("Note", safeNote);

    // container list
    List<MapObject> containers = new ArrayList<>();

    for (Container container : inquiry.getContainers()) {
      MapObject rec = new MapObject();
      ContainerType containerType = ContainerTypeUnit.match(container.getContainerType());
      if (containerType != null) {
        rec.set("ContainerType", containerType.getLabel());
      } else {
        rec.set("ContainerType", container.getContainerType());
      }
      rec.set("Quantity", container.getQuantity());
      rec.set("ContainerNo", "");
      rec.set("ContainerSeal", "");
      containers.add(rec);
    }
    ibooking.set("Containers", containers);
    List<MapObject> collected = new ArrayList<>();
    for (SellingRate rate : this.sellingRates) {
      MapObject rec = rate.toBFSOneFee(customer);
      collected.add(rec);
    }
    ibooking.set("SellingRates", collected);
    return ibooking;
  }

  private String computeServiceType() {
    TransportationMode mode = inquiry.getMode();
    Purpose purpose = inquiry.getPurpose();

    String serviceType;
    if (TransportationMode.isSeaTransport(mode)) {
      serviceType = purpose == Purpose.EXPORT ? "SeaExpTransactions" : "SeaImpTransactions";
      if (TransportationMode.isSeaFCLTransport(mode)) {
        serviceType += "_FCL";
      } else if (TransportationMode.isSeaLCLTransport(mode)) {
        serviceType += "_LCL";
      }
    } else if (TransportationMode.isAirTransport(mode)) {
      serviceType = purpose == Purpose.EXPORT ? "AirExpTransactions" : "AirImpTransactions";
    } else if (TransportationMode.isTruckTransport(mode)) {
      serviceType = "InlandTrucking";
    } else {
      serviceType = "CustomsLogistics";
    }

    if (StringUtil.isEmpty(serviceType)) {
      throw new RuntimeError(ErrorType.IllegalArgument, "Invalid transportation mode: " + mode);
    }
    return serviceType;
  }

  public BookingModel computeInquiry() {
    inquiry.setSalemanAccountId(senderAccountId);
    inquiry.setSalemanLabel(senderLabel);
    inquiry.setFromLocationCode(fromLocationCode);
    inquiry.setFromLocationLabel(fromLocationLabel);
    inquiry.setToLocationCode(toLocationCode);
    inquiry.setToLocationLabel(toLocationLabel);
    inquiry.setContainerTypes(unitOfPackage);
    inquiry.setGrossWeightKg(grossWeight);
    inquiry.setVolumeCbm(volume);
    inquiry.setDescOfGoods(descriptionOfGoods);
    inquiry.setCommodity(commodity);
    inquiry.setPackageQty(packages);
    inquiry.setPickupAddress(cargoPickupAt);
    inquiry.setDeliveryAddress(cargoDeliveryAt);
    inquiry.computeTypeOfService();
    return this;
  }

  public BookingModel withInquiry(SpecificServiceInquiry inquiry) {
    this.inquiry = inquiry;

    // Load inquiry fields
    if (inquiry != null) {
      this.senderAccountId = inquiry.getSalemanAccountId();
      this.senderLabel = inquiry.getSalemanLabel();
      this.fromLocationCode = inquiry.getFromLocationCode();
      this.fromLocationLabel = inquiry.getFromLocationLabel();
      this.toLocationCode = inquiry.getToLocationCode();
      this.toLocationLabel = inquiry.getToLocationLabel();
      this.cargoPickupAt = inquiry.getPickupAddress();
      this.cargoDeliveryAt = inquiry.getDeliveryAddress();
      this.unitOfPackage = inquiry.getContainerTypes();
      this.grossWeight = inquiry.getGrossWeightKg();
      this.volume = inquiry.getVolumeCbm();
      this.commodity = inquiry.getCommodity();
      this.descriptionOfGoods = inquiry.getDescOfGoods();
      this.packages = inquiry.getPackageQty();
    }

    return this;
  }

}