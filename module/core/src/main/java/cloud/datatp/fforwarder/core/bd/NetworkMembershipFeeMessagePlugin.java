package cloud.datatp.fforwarder.core.bd;

import org.springframework.stereotype.Component;

import cloud.datatp.fforwarder.core.message.MessageServicePlugin;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import net.datatp.security.client.ClientContext;

@Component
public class NetworkMembershipFeeMessagePlugin extends MessageServicePlugin {
  public static final String PLUGIN_TYPE = "bd-network-membership-fee-notification";
  
  public NetworkMembershipFeeMessagePlugin() {
    super(PLUGIN_TYPE);
  }
  
  public void onPostSend(ClientContext client, CRMMessageSystem msg) {
    
  }
  
  public void onSendError(ClientContext client, CRMMessageSystem message, Exception error) throws Exception {
    
  }
  
}
