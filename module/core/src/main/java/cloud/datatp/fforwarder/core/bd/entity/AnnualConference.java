package cloud.datatp.fforwarder.core.bd.entity;

import java.io.Serial;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import cloud.datatp.fforwarder.core.bd.AnnualConferenceMessagePlugin;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.security.client.ClientContext;
import net.datatp.util.bean.BeanUtil;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;

/**
 * <AUTHOR>
 */
@Entity
@Table(
  name = AnnualConference.TABLE_NAME,
  indexes = {
    @Index(
      name = AnnualConference.TABLE_NAME + "_network_idx",
      columnList = "network"
    ),
  }
)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class AnnualConference extends PersistableEntity<Long> {

  @Serial
  private static final long serialVersionUID = 1L;

  final static public String TABLE_NAME = "lgc_forwarder_crm_annual_conference";

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "date_created")
  private Date dateCreated = new Date();

  @Column(nullable = false)
  private String network;

  @Column(nullable = false, length = 1024)
  private String event;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "start_date")
  private Date startDate;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "end_date")
  private Date endDate;

  @Column(length = 1024)
  private String venue;

  // Phí tham dự
  @Column(name = "delegates_fee", length = 2 * 1024)
  private String delegatesFee;

  @Column(name = "registration_period")
  private String registrationPeriod;

  @Column(name = "expected_attendance", length = 50)
  private Integer expectedAttendance;

  // Đại biểu được phân công (assigned delegates)
  @Column(name = "assigned_delegates")
  private String assignedDelegates;

  // Ghi chú
  @Column(name = "note", length = 4 * 1024)
  private String note;

  @Column(name = "auto_reminder")
  private boolean autoReminder = true;

  @Column(name = "notification_time")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date notificationTime;

  @Column(name = "input_by_account_id")
  private Long inputByAccountId;
  
  @Column(name = "input_by_account_label")
  private String inputByAccountLabel;
  
  @Transient
  private String inputByEmail;

  public AnnualConference mergeFromMapObject(MapObject mapObject) {
    List<String> fields = List.of(
        "dateCreated", "network", "event", "startDate", "endDate", "venue",
        "delegatesFee", "registrationPeriod", "expectedAttendance", "assignedDelegates",
        "note", "autoReminder", "notificationTime", "notificationTime", "inputByAccountId", "inputByAccountLabel"
      );

    BeanUtil.updateFieldsFromMap(this, mapObject, fields);
    
    return this;
  }
  
  public CRMMessageSystem toCRMMessage(ClientContext client) {
    Objects.assertNotNull(!isNew(), "New Annual Conference cannot be send message to handler");
    CRMMessageSystem msg = new CRMMessageSystem();
    msg.setContent(buildMailMsg(client));
    msg.setScheduledAt(notificationTime != null ? notificationTime : new Date());
    msg.setMessageType(MessageType.MAIL);
    msg.setReferenceId(id);
    msg.setReferenceType(TABLE_NAME);
    msg.setPluginName(AnnualConferenceMessagePlugin.PLUGIN_TYPE);
    msg.setRecipients(new HashSet<>(Arrays.asList(inputByEmail)));
    
    MapObject metadata = new MapObject();
    metadata.put("fromEmail", "<EMAIL>");
    metadata.put("subject", String.format("CRM - Annual Conference"));
    metadata.put("to", Arrays.asList(inputByEmail));
    msg.setMetadata(metadata);
    
    return msg;
  }
  
  private String buildMailMsg(ClientContext client) {
    return String.format("""
        <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 15px; background-color: #f9fafb;">
            <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h1 style="color: #1f2937; font-size: 20px; margin: 0 0 16px 0; padding-bottom: 12px; border-bottom: 1px solid #e5e7eb;">
                    PRICE CHECK REQUEST - REF: %s
                </h1>
                <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                    <p style="margin: 0 0 8px 0; color: #374151;">
                        <strong style="color: #1f2937;">👨‍💼 Sales:</strong> %s
                    </p>
                    <p style="margin: 0; color: #374151;">
                        <strong style="color: #1f2937;">📧 Email:</strong> %s
                    </p>
                </div>
                %s
                <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                    <p style="margin: 0 0 8px 0; color: #374151;">
                        <strong style="color: #1f2937;">📋 Subject:</strong> %s
                    </p>
                    <p style="margin: 0 0 8px 0; color: #374151;">
                        <strong style="color: #1f2937;">💬 Feedback:</strong> %s
                    </p>
                    <p style="margin: 0 0 8px 0; color: #374151;">
                        <strong style="color: #1f2937;">📝 Pricing Note:</strong> %s
                    </p>
                    <p style="margin: 0 0 8px 0; color: #374151;">
                        <strong style="color: #1f2937;">👨‍💼 Pricing By:</strong> %s
                    </p>
                    <p style="margin: 0 0 8px 0; color: #374151;">
                        <strong style="color: #1f2937;">📊 Status:</strong> %s
                    </p>
                    <p style="margin: 0; color: #374151;">
                        <strong style="color: #1f2937;">👤 Updated by:</strong> %s
                    </p>
                </div>
                <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                    <p style="color: #6b7280; font-size: 14px; margin: 0;">
                        This is an automated notification from the CRM Task Management System.
                    </p>
                </div>
            </div>
        </div>
      """,
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      ""
    );
  }

}