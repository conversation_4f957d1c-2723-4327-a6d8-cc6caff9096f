package cloud.datatp.fforwarder.core.bd;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fforwarder.core.bd.entity.NetworkMembershipFee;
import cloud.datatp.fforwarder.core.bd.repository.NetworkMembershipFeeRepository;
import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.db.CRMSqlQueryUnitManager;
import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;

@Slf4j
@Component
public class NetworkMembershipFeeLogic extends CRMDaoService {
  
  @Autowired
  private NetworkMembershipFeeRepository repo;
  
  @Autowired
  private AccountLogic accountLogic;
  
  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;
  
  public NetworkMembershipFee getById(ClientContext client, Long id) {
    return repo.getById(id);
  }
  
  public NetworkMembershipFee save(ClientContext client, NetworkMembershipFee fee) {
    fee.set(client);
    
    if (fee.getDateCreated() == null) fee.setDateCreated(new Date());
    
    if (fee.getInputByAccountId() == null) {
      Account clientAcc = accountLogic.getAccountById(client, client.getAccountId());
      fee.setInputByAccountId(clientAcc.getId());
      fee.setInputByAccountLabel(clientAcc.getFullName());
    }
    
    NetworkMembershipFee saved = repo.save(fee);
    
    Account inputByAccount = accountLogic.getAccountById(client, saved.getInputByAccountId());
    Objects.assertNotNull(inputByAccount, "Account is not found by id = {}", saved.getInputByAccountId());
    
    saved.setInputByEmail(inputByAccount.getEmail());
    
    CRMMessageSystem message = saved.toCRMMessage(client);
    crmMessageLogic.scheduleMessage(client, message);
    
    return saved;
  }
  
  public List<MapObject> saveRecords(ClientContext client, List<MapObject> records) {
    if (Collections.isEmpty(records)) return records;
    
    for (MapObject record : records) {
      
      NetworkMembershipFee fee = new NetworkMembershipFee();
      Long id = record.getLong("id", null);
      if (id != null) fee = getById(client, id);
      fee = fee.mergeFromMapObject(record);
      
      if (fee.getDateCreated() == null) fee.setDateCreated(new Date());
      
      if (fee.getInputByAccountId() == null) {
        Account clientAcc = accountLogic.getAccountById(client, client.getAccountId());
        fee.setInputByAccountId(clientAcc.getId());
        fee.setInputByAccountLabel(clientAcc.getFullName());
      }
      
      NetworkMembershipFee saved = save(client, fee);
      record.put("id", saved.getId());
    }
    
    return records;
  }
  
  public List<SqlMapRecord> searchNetworkMembershipFees(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "company-logistics-sales");
    if (permission == null) return new ArrayList<>();
    try {
      sqlParams.addParam("accessAccountId", client.getAccountId());
      
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/BDSql.groovy";
      String scriptName = "SearchNetworkMembershipFee";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(crmDataSource, sqlParams);
      return view.renameColumWithJavaConvention().getSqlMapRecords();
    } catch (Exception e) {
      log.error("Error when search Network Membership Free", e);
      return new ArrayList<>();
    }
  }
  
  public boolean deleteByIds(ClientContext client, List<Long> ids) {
    repo.deleteAllById(ids);
    return true;
  }
  
  public boolean updateStorageState(ClientContext client, ChangeStorageStateRequest req) {
    repo.updateStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }
}