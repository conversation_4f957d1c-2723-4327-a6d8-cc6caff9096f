package cloud.datatp.fforwarder.core.message;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.app.AppEnv;
import net.datatp.module.bot.BotEvent;
import net.datatp.module.bot.BotService;
import net.datatp.module.bot.cron.CronJob;
import net.datatp.module.bot.cron.CronJobFrequency;
import net.datatp.module.bot.cron.CronJobLogger;
import net.datatp.module.bot.task.TaskUnitBotEvent;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.monitor.SourceType;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DeviceInfo;
import net.datatp.security.client.DeviceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CRMMessageSenderCronJob extends CronJob {

  @Autowired
  private AppEnv appEnv;

  @Autowired
  private BotService botService;

  public CRMMessageSenderCronJob() {
    super("lgc-forwarder-crm-message-sender:1m", "CRM Message Sender Cron Job (1min)");
  }

  @PostConstruct
  public void onInit() {
    if (appEnv.isProdEnv()) {
      setFrequencies(CronJobFrequency.EVERY_MINUTE);
    } else {
      setFrequencies(CronJobFrequency.EVERY_MINUTE);
    }
  }

  @Override
  protected List<ICompany> getTargetCompanies() {
    List<ICompany> companies = new ArrayList<>();
    ICompany company = ICompany.SYSTEM;
    companies.add(company);
    return companies;
  }

  @Override
  protected ClientContext getClientContext(ICompany company) {
    ClientContext client = new ClientContext("default", "dan", "localhost");
    if (appEnv.isProdEnv()) {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Server));
    } else {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Computer));
    }
    client.setAccountId(3L);
    client.setCompany(company);
    return client;
  }

  protected Set<String> getReportToUsers(ClientContext client, ICompany company) {
    Set<String> userSet = super.getReportToUsers(client, company);
    userSet.add("dan");
    return userSet;
  }

  @Override
  protected void run(ClientContext client, ICompany company, CronJobLogger logger) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");

    ExecutableContext ctx =
      new ExecutableContext(client, company)
        .withScriptEnv(
          scriptDir,
          CRMMessageSenderExecutor.class,
          CRMMessageSenderExecutor.CRMMessageSenderUnit.class);

    BotEvent<?> botEvent =
      new TaskUnitBotEvent(client, company, ctx)
        .withProcessMode(BotEvent.ProcessMode.Immediately)
        .withReportToUsers(getReportToUsers(client, company));

    botService.broadcast(SourceType.UserBot, botEvent);

  }
}