package net.datatp.module.core.security.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.ICompanyEntity;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.security.client.AccessType;
import net.datatp.security.client.Capability;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DataScope;

import java.io.Serial;

@Entity
@JsonInclude(Include.NON_NULL)
@Table(
  name = AppPermission.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = AppPermission.TABLE_NAME + "_account_id_app_id_company_id",
      columnNames = {"company_id", "account_id", "app_id"}
    ),
  },
  indexes = {
    @Index(columnList = "account_id")
  }
)
@NoArgsConstructor
@Getter @Setter
public class AppPermission extends PersistableEntity<Long> implements ICompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "security_app_permission";

  @NotNull
  @Column(name = "company_id")
  private Long companyId;

  @Column(name = "app_id")
  private Long appId;

  @NotNull
  @Column(name = "account_id", nullable = false)
  private Long accountId;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "access_type")
  private AccessType accessType = AccessType.Employee;

  @Column(name = "app_role")
  private String appRole;

  @Column(name = "app_role_label")
  private String appRoleLabel;

  @NotNull
  @Enumerated(EnumType.STRING)
  private Capability capability;

  @Column(name = "data_scope")
  @Enumerated(EnumType.STRING)
  private DataScope dataScope;

  public Long companyId() {
    return companyId;
  }

  public void withCompanyId(Long id) {
    this.companyId = id;
  }

  public AppPermission withCapability(Capability capability) {
    this.capability = capability;
    return this;
  }

  public AppPermission withApp(App app) {
    this.appId = app.getId();
    return this;
  }

  public AppPermission withType(AccessType type) {
    this.accessType = type;
    return this;
  }

  public boolean isOwnerScope() {
    return this.dataScope == DataScope.Owner;
  }

  public boolean isGroupScope() {
    return this.dataScope == DataScope.Group;
  }

  public boolean isCompanyScope() {
    return this.dataScope == DataScope.Company;
  }

  public boolean isAllScope() {
    return this.dataScope == DataScope.All;
  }

  @Override
  public void set(ClientContext client, Long companyId) {
    super.set(client);
    this.companyId = companyId;
  }
}