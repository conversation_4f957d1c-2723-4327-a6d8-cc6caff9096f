package cloud.datatp.fforwarder.price.common;

import cloud.datatp.fforwarder.core.common.Purpose;
import cloud.datatp.fforwarder.core.common.TransportationMode;

import java.util.HashMap;
import java.util.Map;

public class ExcelParserHelper {

  public static Map<String, String> getTemplateUpload(TransportationMode mode, Purpose purpose) {
    if (TransportationMode.isSeaFCLTransport(mode)) return ExcelParserHelper.getSeaFCLTemplate(purpose);
    if (TransportationMode.isSeaLCLTransport(mode)) return ExcelParserHelper.getSeaLCLTemplate(purpose);
    if (TransportationMode.isAirTransport(mode)) return ExcelParserHelper.getAirTemplate(purpose);
    if (TransportationMode.isTruckContainer(mode)) return ExcelParserHelper.getContainerTemplate();
    if (TransportationMode.isTruckRegular(mode)) return ExcelParserHelper.getTruckingTemplate();
    throw new RuntimeException("Unsupported mode: " + mode + " and purpose = " + purpose);
  }

  public static Map<String, String> getSeaFCLTemplate(Purpose purpose) {
    Map<String, String> templateMap = new HashMap<>();
    templateMap.put("POL", "fromLocationCode");
    templateMap.put("POL Code", "fromLocationCode");

    templateMap.put("POD", "toLocationCode");
    templateMap.put("POD Code", "toLocationCode");

    templateMap.put("Terminal at POD", "finalTerminalLocationLabel");
    templateMap.put("Final Destination", "finalTerminalLocationLabel");

    templateMap.put("Group Type", "groupType");

    templateMap.put("Mode", "containerHandlingType");

    templateMap.put("Carrier Partner Id", "carrierPartnerId");
    templateMap.put("Carrier", "carrierLabel");
    templateMap.put("Agent", "handlingAgentPartnerLabel");

    templateMap.put("20DC", "dry20Price");
    templateMap.put("40DC", "dry40Price");
    templateMap.put("20GP", "dry20Price");
    templateMap.put("40GP", "dry40Price");

    templateMap.put("40HC", "highCube40Price");
    templateMap.put("45HC", "highCube45Price");
    templateMap.put("40NOR", "nor40Price");

    templateMap.put("20RF", "reefer20Price");
    templateMap.put("40RF", "reefer40Price");

    templateMap.put("Currency", "currency");
    templateMap.put("Freetime", "freeTime");
    templateMap.put("Free time", "freeTime");
    templateMap.put("Note", "note");
    templateMap.put("Valid From", "validFrom");
    templateMap.put("Valid To", "validTo");
    templateMap.put("Frequency", "frequency");
    templateMap.put("Transit Port", "transitPort");

    templateMap.put("T/T (days)", "transitTime");
    templateMap.put("Transit Time", "transitTime");

    templateMap.put("Surcharge", "surchargeNote");
    templateMap.put("Cut off time", "cutoff");
    templateMap.put("ETD", "etd");

    if (Purpose.IMPORT.equals(purpose)) {
      // FCL Import
      templateMap.put("Telex /SWB", "addCharge:TLX:SHIPMENT");
      templateMap.put("Telex charge/SWB (USD/MAWB)", "addCharge:TLX:SHIPMENT");
    } else {
      // FCL Export
      templateMap.put("Commodity", "commodity");
      templateMap.put("Remarks", "remarks");

      templateMap.put("20DC (KB)", "commissionDry20Price");
      templateMap.put("40DC (KB)", "commissionDry40Price");

      templateMap.put("40HC (KB)", "commissionHighCube40Price");
      templateMap.put("45HC (KB)", "commissionHighCube45Price");
      templateMap.put("40NOR (KB)", "commissionNor40Price");

      templateMap.put("20RF (KB)", "commissionReefer20Price");
      templateMap.put("40RF (KB)", "commissionReefer40Price");
    }
    return templateMap;
  }


  public static Map<String, String> getSeaLCLTemplate(Purpose purpose) {
    Map<String, String> templateMap = new HashMap<>();
    templateMap.put("POL Code", "fromLocationCode");
    templateMap.put("POL", "fromLocationCode");
    templateMap.put("POD Code", "toLocationCode");
    templateMap.put("POD", "toLocationCode");

    templateMap.put("Carrier", "carrierLabel");

    templateMap.put("Currency", "currency");
    templateMap.put("Stuffing", "stuffingNote");
    templateMap.put("Surcharge", "surchargeNote");

    templateMap.put("Note", "note");

    templateMap.put("LCC DEST", "localChargeAtDest");

    templateMap.put("Valid From", "validFrom");
    templateMap.put("Valid Date from", "validFrom");

    templateMap.put("Valid To", "validTo");
    templateMap.put("Valid Date to", "validTo");

    templateMap.put("Frequency", "frequency");
    templateMap.put("Via", "transitPort");
    templateMap.put("Transit port", "transitPort");

    templateMap.put("T/T (days)", "transitTime");
    templateMap.put("Transit Time", "transitTime");
    templateMap.put("Free time", "freeTime");

    // LCL Import
    templateMap.put("Freight (CBM)", "freightChargeLCL");

    templateMap.put("Freight (USD/CBM)", "freightChargeLCL");

    if (Purpose.IMPORT.equals(purpose)) {
      templateMap.put("Agent", "handlingAgentPartnerLabel");
      templateMap.put("Agent Name", "handlingAgentPartnerLabel");

      templateMap.put("Minimum (< 1 CBM)", "minimumChargeLCL");
      templateMap.put("< 2 CBM", "less2CbmPrice");
      templateMap.put("< 3 CBM", "less3CbmPrice");
      templateMap.put("< 5 CBM", "less5CbmPrice");
      templateMap.put("< 7 CBM", "less7CbmPrice");
      templateMap.put("< 10 CBM", "less10CbmPrice");
      templateMap.put("< 15 CBM", "geq10CbmPrice");

      templateMap.put("ETD", "etd");
      templateMap.put("Cut off time", "cutoff");

      templateMap.put("D/O (USD/BL)", "addCharge:DO:SET");
      templateMap.put("D/O (per SET)", "addCharge:DO:SET");

      templateMap.put("CFS (USD/CBM)", "addCharge:CFS:CBM");
      templateMap.put("CFS (per RT)", "addCharge:CFS:CBM");

      templateMap.put("THC (USD/CBM)", "addCharge:THC:CBM");
      templateMap.put("THC (per RT)", "addCharge:THC:CBM");

      templateMap.put("CIC (USD/CBM)", "addCharge:CIC:CBM");
      templateMap.put("CIC (per RT)", "addCharge:CIC:CBM");
    } else {
      // LCL Export
      templateMap.put("CFS (USD/CBM)", "addCharge:CFS:CBM");
      templateMap.put("THC (USD/CBM)", "addCharge:THC:CBM");
      templateMap.put("EBS (USD/CBM)", "addCharge:EBS:CBM");
      templateMap.put("LSS (USD/CBM)", "addCharge:LSS:CBM");
      templateMap.put("RR (USD/CBM)", "addCharge:RR:CBM");
      templateMap.put("GRI (USD/CBM)", "addCharge:GRI:CBM");
      templateMap.put("DDC (USD/CBM)", "addCharge:DDC:CBM");
      templateMap.put("AFR/AMS/ISF (USD/SET)", "addCharge:B_AFR:SET");
      templateMap.put("BILL (USD/SET)", "addCharge:BILL:SET");
    }

    return templateMap;
  }


  public static Map<String, String> getContainerTemplate() {
    Map<String, String> templateMap = new HashMap<>();

    //TODO: should be remove, using Pickup Location/ Province
    templateMap.put("Pickup Location", "pickupLocationCode");
    templateMap.put("Province/ City (Pickup)", "pickupProvince");

    templateMap.put("Pickup Location/ Province", "pickupLocationCode");
    templateMap.put("Pickup Address", "pickupAddress");

    //TODO: should be remove, using Delivery Location/ Province
    templateMap.put("Delivery Location", "deliveryLocationCode");
    templateMap.put("Province/ City (Delivery)", "deliveryProvince");

    templateMap.put("Delivery Location/ Province", "deliveryLocationCode");
    templateMap.put("Delivery Address", "deliveryAddress");

    templateMap.put("Km2Way", "km2way");
    templateMap.put("Currency", "currency");
    templateMap.put("Note", "note");
    templateMap.put("Valid From", "validFrom");
    templateMap.put("Valid To", "validTo");

    templateMap.put("Partner Reference", "targetReferenceLabel");

    templateMap.put("Transit Border", "transitBorder");

    templateMap.put("Coloader/Agent", "handlingAgentPartnerLabel");
    templateMap.put("Frequency", "frequency");
    templateMap.put("Transit Time", "transitTime");
    templateMap.put("Transit time", "transitTime");

    templateMap.put("20DC:LT-10T", "contDry20Lt10TonPrice");
    templateMap.put("20DC:LT-21T", "contDry20Geq10TonPrice");
    templateMap.put("20DC:GEQ-21T", "contDry20Geq17TonPrice");
    templateMap.put("40HC:LT-18T", "contDry40Lt17TonPrice");
    templateMap.put("40HC:GEQ-18T", "contDry40Geq17TonPrice");

    templateMap.put("20HC:LT-17T", "contHighCube20Lt17TonPrice");
    templateMap.put("20HC:GEQ-17T", "contHighCube20Geq17TonPrice");
    templateMap.put("40HC:LT-17T", "contHighCube40Lt17TonPrice");
    templateMap.put("40HC:GEQ-17T", "contHighCube40Geq17TonPrice");

    templateMap.put("20RF:LT-17T", "contReefer20Lt17TonPrice");
    templateMap.put("20RF:GEQ-17T", "contReefer20Geq17TonPrice");
    templateMap.put("40RF:LT-17T", "contReefer40Lt17TonPrice");
    templateMap.put("40RF:GEQ-17T", "contReefer40Geq17TonPrice");

    templateMap.put("20OT:LT-17T", "contOpenTop20Lt17TonPrice");
    templateMap.put("20OT:GEQ-17T", "contOpenTop20Geq17TonPrice");
    templateMap.put("40OT:LT-17T", "contOpenTop40Lt17TonPrice");
    templateMap.put("40OT:GEQ-17T", "contOpenTop40Geq17TonPrice");

    templateMap.put("20FR:LT-17T", "contFlatRack20Lt17TonPrice");
    templateMap.put("20FR:GEQ-17T", "contFlatRack20Geq17TonPrice");
    templateMap.put("40FR:LT-17T", "contFlatRack40Lt17TonPrice");
    templateMap.put("40FR:GEQ-17T", "contFlatRack40Geq17TonPrice");

    templateMap.put("20T:LT-17T", "contTank20Lt17TonPrice");
    templateMap.put("20T:GEQ-17T", "contTank20Geq17TonPrice");
    templateMap.put("40T:LT-17T", "contTank40Lt17TonPrice");
    templateMap.put("40T:GEQ-17T", "contTank40Geq17TonPrice");

    templateMap.put("Cont 40HC", "contHighCube40Price");
    templateMap.put("Cont 45HC", "contHighCube45Price");
    templateMap.put("Cont 40RF", "contReefer40Price");
    templateMap.put("Cont 45RF", "contReefer45Price");

    templateMap.put("Custom fee at Lang Son", "customFeeAtLangSon");
    templateMap.put("Custom fee at Pinxiang", "customFeeAtPinXiang");

    templateMap.put("Oil Price In Effect", "oilPriceInEffect");
    templateMap.put("Effective Date", "validFrom");
    templateMap.put("Valid To Date", "validTo");

    templateMap.put("Subcontractor", "carrierLabel");
    templateMap.put("Carrier Partner Id", "carrierPartnerId");

    templateMap.put("Cross Border Trucking", "isCBT");
    return templateMap;
  }

  public static Map<String, String> getTruckingTemplate() {
    Map<String, String> templateMap = new HashMap<>();
    //TODO: should be remove, using Pickup Location/ Province
    templateMap.put("Pickup Location", "pickupLocationCode");
    templateMap.put("Province/ City (Pickup)", "pickupProvince");

    templateMap.put("Pickup Location/ Province", "pickupLocationCode");
    templateMap.put("Pickup Address", "pickupAddress");

    //TODO: should be remove, using Delivery Location/ Province
    templateMap.put("Delivery Location", "deliveryLocationCode");
    templateMap.put("Province/ City (Delivery)", "deliveryProvince");

    templateMap.put("Delivery Location/ Province", "deliveryLocationCode");
    templateMap.put("Delivery Address", "deliveryAddress");

    templateMap.put("Km2Way", "km2way");
    templateMap.put("Currency", "currency");
    templateMap.put("Note", "note");
    templateMap.put("Valid From", "validFrom");
    templateMap.put("Valid To", "validTo");

    templateMap.put("Partner Reference", "targetReferenceLabel");

    templateMap.put("Transit Border", "transitBorder");
    templateMap.put("Coloader/Agent", "handlingAgentPartnerLabel");
    templateMap.put("Frequency", "frequency");
    templateMap.put("Transit Time", "transitTime");

    templateMap.put("1.25T", "truck1Ton25Price");
    templateMap.put("1.5T", "truck1Ton5Price");
    templateMap.put("2.5T", "truck2Ton5Price");
    templateMap.put("3.5T", "truck3Ton5Price");
    templateMap.put("5T", "truck5TonPrice");
    templateMap.put("7T", "truck7TonPrice");
    templateMap.put("8T", "truck8TonPrice");
    templateMap.put("9T", "truck9TonPrice");
    templateMap.put("10T", "truck10TonPrice");
    templateMap.put("12T", "truck12TonPrice");
    templateMap.put("15T", "truck15TonPrice");
    templateMap.put("30T", "truck30TonPrice");

    templateMap.put("Custom fee at Lang Son", "customFeeAtLangSon");
    templateMap.put("Custom fee at Pinxiang", "customFeeAtPinXiang");

    templateMap.put("USD / Kg (Stackable)", "stackableUSDPerKG");
    templateMap.put("USD / Kg (Nonstackable)", "nonstackableUSDPerKG");

    templateMap.put("Oil Price In Effect", "oilPriceInEffect");
    templateMap.put("Effective Date", "validFrom");
    templateMap.put("Valid To Date", "validTo");
    templateMap.put("Subcontractor", "carrierLabel");

    templateMap.put("Carrier Partner Id", "carrierPartnerId");

    templateMap.put("Cross Border Trucking", "isCBT");

    return templateMap;
  }

  public static Map<String, String> getAirTemplate(Purpose purpose) {
    Map<String, String> templateMap = new HashMap<>();
    templateMap.put("AOL", "fromLocationCode");
    templateMap.put("AOL Code", "fromLocationCode");
    templateMap.put("AOD", "toLocationCode");
    templateMap.put("AOD Code", "toLocationCode");

    templateMap.put("Airline", "carrierLabel");

    templateMap.put("Minimum", "minPrice");
    templateMap.put("-45(KG)", "level1Price");
    templateMap.put("+45(KG)", "level2Price");
    templateMap.put("+100(KG)", "level3Price");
    templateMap.put("+250(KG)", "level4Price");
    templateMap.put("+300(KG)", "level5Price");
    templateMap.put("+500(KG)", "level6Price");
    templateMap.put("+1000(KG)", "level7Price");

    templateMap.put("Currency", "currency");

    templateMap.put("ROE (VND/USD)", "roeVNDToUSD");
    templateMap.put("Flight No.", "flightNote");


    templateMap.put("CTNS/ PALLET", "packingNote");
    templateMap.put("Commodity", "commodity");

    templateMap.put("Note", "note");

    templateMap.put("Valid From", "validFrom");
    templateMap.put("Created Date", "validFrom");
    templateMap.put("Valid Date from", "validFrom");

    templateMap.put("Valid To", "validTo");
    templateMap.put("Valid Date to", "validTo");

    templateMap.put("ETD", "etd");
    templateMap.put("Cut off time", "cutoff");
    templateMap.put("Frequency", "frequency");
    templateMap.put("Transit Port", "transitPort");
    templateMap.put("T/T (days)", "transitTime");
    templateMap.put("Transit Time", "transitTime");

    templateMap.put("FSC (USD/KGS)", "addCharge:FSC:KGM");
    templateMap.put("SCC (USD/KGS)", "addCharge:SCC:KGM");
    templateMap.put("AMS (USD/HAWB)", "addCharge:AMS:HAWB");
    templateMap.put("AMS (USD/MAWB)", "addCharge:AMS:MAWB");
    templateMap.put("AWB (USD/SET)", "addCharge:AWB:SET");
    templateMap.put("XRAY (USD/KGS)", "addCharge:XRAY:KGM");
    templateMap.put("TCS (USD/KGS)", "addCharge:TCS:KGM");
    templateMap.put("FHL (USD/SET)", "addCharge:FHL:SET");
    templateMap.put("FWB (USD/SET)", "addCharge:FWB:SET");
    templateMap.put("CCA (USD/SET)", "addCharge:CCA:SET");
    templateMap.put("DG (USD/SET)", "addCharge:DG:SET");

    if (Purpose.IMPORT.equals(purpose)) {
      templateMap.put("Agent", "handlingAgentPartnerLabel");
    }

    return templateMap;
  }
}