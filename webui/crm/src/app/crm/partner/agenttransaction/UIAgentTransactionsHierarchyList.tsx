import React from 'react';
import * as FeatherIcon from 'react-feather';
import { grid, entity, bs, util, app } from '@datatp-ui/lib';

import { T } from '../../price';
import { responsiveGridConfig } from '../../sales/common';
import {
  UIVolumeSalemanKeyAccountReportList,
} from '../../sales/partners/report/UIVolumeSalemanKeyAccountReportList';
import { AgentTransactionReportModel, AgentTransactionsReportFilter, UIAgentTransactionsListPlugin } from './UIAgentTransactionsPage';
import { AgentTransactionsTreePlugin, getTreePlugin } from './UIAgentTransactionsHierarchyListUtils';

export type Space = 'User' | 'Company' | 'System'

interface UIAgentTransactionsHierarchyProps extends entity.DbEntityListProps {
  reportFilter?: AgentTransactionsReportFilter;
  agentTransactionReportModel?: AgentTransactionReportModel;
}
export class UIAgentTransactionsHierarchy extends entity.DbEntityList<UIAgentTransactionsHierarchyProps> {

  createVGridConfig(): grid.VGridConfig {
    let { agentTransactionReportModel, reportFilter } = this.props;
    const groupedBy = reportFilter?.groupedBy || { label: 'Country', value: "COUNTRY" };
    const treePlugin: AgentTransactionsTreePlugin = getTreePlugin(groupedBy.value);
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 35,
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'label', label: T('Hierarchy'), width: 400, filterable: true, container: 'fixed-left',
            fieldDataGetter(record) {
              return record['label'] || 'N/A';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;

              let label = record[field.name] || 'N/A';

              let type = record['groupType'];

              if (type === 'TOTAL') {
                return (
                  <div className="d-flex align-items-start w-100 fw-bold"
                    onClick={() => this.onDefaultSelect(dRec)} style={{ cursor: 'pointer', userSelect: 'text' }}>
                    <h6 className="fw-bold text-warning"> <FeatherIcon.Package className="me-2" size={16} />{label}</h6>
                  </div>
                )
              }

              const onClick = (dRecord: grid.DisplayRecord) => {
                if (type === 'AGENT') {
                  this.onDefaultSelect(dRecord);
                } else {
                  dRecord.model['collapse'] = !dRecord.model['collapse'];
                  let displayRecordList = _ctx.model.getDisplayRecordList();
                  if (displayRecordList instanceof grid.TreeDisplayModel) {
                    displayRecordList.updateDisplayRecords();
                    _ctx.getVGrid().forceUpdateView();
                  }
                }
              }

              let icon = FeatherIcon.File;
              let color = 'secondary';
              let htmlLines: any[] = []

              switch (type) {
                case 'NETWORK':
                  icon = FeatherIcon.Globe;
                  color = 'info';
                  break;
                case 'CONTINENT':
                  icon = FeatherIcon.Map;
                  color = 'primary';
                  break;
                case 'COUNTRY':
                  icon = FeatherIcon.MapPin;
                  color = 'success';
                  break;
                case 'AGENT':
                  icon = FeatherIcon.User;
                  color = 'secondary';

                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Agent Name'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {`${record['agentCode']} - ${record['label']}`}
                      </div>
                    </div>
                  );

                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Source'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {`${record['source']}`}
                      </div>
                    </div>
                  );


                  if (record['dateCreated']) {
                    let dateCreated = util.text.formater.compactDate(record['dateCreated']);
                    htmlLines.push(
                      <div className='flex-vbox border-0'>
                        <div className="tooltip-header">
                          <span className="tooltip-title">{'Created Date'}:</span>
                        </div>
                        <div className="tooltip-body">
                          {`${dateCreated}`}
                        </div>
                      </div>
                    );
                  }

                  if (record['latestContact'] && record['latestTransactionId']) {
                    let latestContact = util.text.formater.compactDate(record['latestContact']);
                    let latestTransactionId = record['latestTransactionId'];
                    htmlLines.push(
                      <div className='flex-vbox border-0'>
                        <div className="tooltip-header">
                          <span className="tooltip-title">{'Latest Transaction'}:</span>
                        </div>
                        <div className="tooltip-body">
                          {latestContact} - {latestTransactionId}
                        </div>
                      </div>
                    );
                  }

                  if (record['workPhone']) {
                    htmlLines.push(
                      <div className='flex-vbox border-0'>
                        <div className="tooltip-header">
                          <span className="tooltip-title">{'Work Phone'}:</span>
                        </div>
                        <div className="tooltip-body">
                          {record['workPhone']}
                        </div>
                      </div>
                    );
                  }

                  if (record['fax']) {
                    htmlLines.push(
                      <div className='flex-vbox border-0'>
                        <div className="tooltip-header">
                          <span className="tooltip-title">{'Fax'}:</span>
                        </div>
                        <div className="tooltip-body">
                          {record['fax']}
                        </div>
                      </div>
                    );
                  }

                  if (record['address']) {
                    htmlLines.push(
                      <div className='flex-vbox border-0'>
                        <div className="tooltip-header">
                          <span className="tooltip-title">{'Address'}:</span>
                        </div>
                        <div className="tooltip-body">
                          {record['address']}
                        </div>
                      </div>
                    );
                  }

                  break;
              }

              const Icon = icon;

              if (type === 'AGENT') {
                return (
                  <bs.CssTooltip width={480} position='auto' offset={{ x: 300, y: -10 }}>
                    <bs.CssTooltipToggle>
                      <div className="d-flex align-items-start w-100" onClick={() => onClick(dRec)}
                        style={{ cursor: 'pointer', userSelect: 'text' }}>
                        <span className={`d-flex align-items-start px-1 py-1 text-${color}`}>
                          <Icon size={14} className="me-1" />
                          <span>
                            {util.text.formater.uiTruncate(label, 300, true)}
                          </span>
                        </span>
                      </div>
                    </bs.CssTooltipToggle>
                    <bs.CssTooltipContent>
                      <div className='flex-vbox'>
                        {htmlLines}
                      </div>
                    </bs.CssTooltipContent>
                  </bs.CssTooltip>
                );
              }

              let childrenCount = dRec.model.children.length || '';
              const showChildrenCount = type === 'COUNTRY' || type === 'CONTINENT' || type === 'NETWORK';
              return (
                <div className="d-flex align-items-start w-100" onClick={() => onClick(dRec)}
                  style={{ cursor: 'pointer', userSelect: 'text' }}>
                  <span className={`d-flex align-items-start px-1 py-1 text-${color}`}>
                    <Icon size={14} className="me-1" />
                    <span>
                      {util.text.formater.uiTruncate(label, 300, true)}
                      {showChildrenCount && childrenCount > 0 && <span className="ms-1 text-secondary">({childrenCount})</span>}
                    </span>
                  </span>
                </div>
              );
            }
          },

          {
            name: 'nominatedCount', label: T('Shipments'), width: 100,
            fieldDataGetter(record) {
              return record['nominatedCount'] || '-';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let value = dRec.record['nominatedCount'] || '-';
              return (
                <div className={`align-items-center justify-content-end w-100`}
                  style={{ cursor: 'pointer', userSelect: 'text' }}
                  onClick={() => this.onToggleShipmentType(dRec, 'NOMINATED')} >
                  {value}
                </div>
              );
            }
          },
          {
            name: 'nominatedTeus', label: T('FCL (TEUs)'), width: 140, sortable: true,
            fieldDataGetter(record) {
              return record['nominatedTeus'] || '-';
            },
          },
          {
            name: 'nominatedCont20', label: T(`Cont 20'`), width: 130, sortable: true,
            fieldDataGetter(record) {
              return record['nominatedCont20'] || '-';
            },
          },
          {
            name: 'nominatedCont40', label: T(`Cont 40'`), width: 130, sortable: true,
            fieldDataGetter(record) {
              return record['nominatedCont40'] || '-';
            },
          },
          {
            name: 'nominatedCont45', label: T(`Cont 45'`), width: 130, sortable: true,
            fieldDataGetter(record) {
              return record['nominatedCont45'] || '-';
            },
          },
          {
            name: 'nominatedCbm', label: T(`LCL (CBM)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              let value = record['nominatedCbm'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
          },
          {
            name: 'nominatedCw', label: T(`Air (KGS)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              let value = record['nominatedCw'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
          },

          {
            name: 'freehandCount', label: T('Shipments'), width: 100,
            fieldDataGetter(record) {
              return record['freehandCount'] || '-';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let value = dRec.record['freehandCount'] || '-';
              return (
                <div className={`align-items-center justify-content-end w-100`}
                  style={{ cursor: 'pointer', userSelect: 'text' }}
                  onClick={() => this.onToggleShipmentType(dRec, 'FREE-HAND')} >
                  {value}
                </div>
              );
            }
          },
          {
            name: 'freehandTeus', label: T('FCL (TEUs)'), width: 140, sortable: true,
            fieldDataGetter(record) {
              return record['freehandTeus'] || '-';
            },
          },
          {
            name: 'freehandCont20', label: T(`Cont 20'`), width: 130, sortable: true,
            fieldDataGetter(record) {
              return record['freehandCont20'] || '-';
            },
          },
          {
            name: 'freehandCont40', label: T(`Cont 40'`), width: 130, sortable: true,
            fieldDataGetter(record) {
              return record['freehandCont40'] || '-';
            },
          },
          {
            name: 'freehandCont45', label: T(`Cont 45'`), width: 130, sortable: true,
            fieldDataGetter(record) {
              return record['freehandCont45'] || '-';
            },
          },
          {
            name: 'freehandCbm', label: T(`LCL (CBM)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              let value = record['freehandCbm'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
          },
          {
            name: 'freehandCw', label: T(`Air (KGS)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              let value = record['freehandCw'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
          },
          {
            name: 'latestContact', label: T('Last contact'), width: 120,
            format: util.text.formater.compactDate,
            fieldDataGetter(record: any) {
              let value = record['latestContact'];
              if (!value || value === '-') {
                return "-";
              }
              return value;
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let value = record[field.name];

              if (!value || value === '-') {
                return <div className="flex-hbox justify-content-center align-items-center">-</div>;
              }
              try {
                const formattedDate = util.text.formater.compactDate(value);
                return (
                  <div className="flex-hbox justify-content-center align-items-center">
                    {formattedDate || '-'}
                  </div>
                );
              } catch (error) {
                console.warn('Invalid date format:', value);
                return <div className="flex-hbox justify-content-center align-items-center">-</div>;
              }
            }
          },
          { name: 'latestTransactionId', label: T('Last Job ID'), width: 150 },
          { name: 'source', label: T('Source'), width: 150, filterable: true, filterableType: 'options' },
          { name: 'continent', label: T('Continent'), width: 150, filterable: true, filterableType: 'options' },
          { name: 'countryLabel', label: T('Country'), width: 150, filterable: true, filterableType: 'options' },
        ],
        fieldGroups: {
          "Nominated": {
            label: T('Nominated'),
            fields: [
              'nominatedCount', 'nominatedTeus', 'nominatedCont20', 'nominatedCont40', 'nominatedCont45', 'nominatedCbm', 'nominatedCw']
          },
          "Freehand": {
            label: T('Freehand'),
            fields: [
              'freehandCount', 'freehandTeus', 'freehandCont20', 'freehandCont40', 'freehandCont45', 'freehandCbm', 'freehandCw']
          },
        },
      },
      toolbar: { hide: true },
      view: {
        currentViewName: 'tree',
        availables: {
          tree: {
            viewMode: 'tree',
            label: 'Tree View',
            treeField: 'label',
            plugin: treePlugin,
            footer: {
              createRecords: (_ctx: grid.VGridContext) => {
                let companyGroupedRecords = agentTransactionReportModel ? agentTransactionReportModel.companyGroupedRecords : [];

                let total = {
                  groupType: 'TOTAL',
                  label: 'TOTAL',
                  agentName: 'Total',
                  agentCode: 'Total',
                  freehandCount: 0,
                  freehandCbm: 0,
                  freehandCw: 0,
                  freehandCont20: 0,
                  freehandCont40: 0,
                  freehandCont45: 0,
                  freehandTeus: 0,
                  nominatedCount: 0,
                  nominatedCbm: 0,
                  nominatedCw: 0,
                  nominatedCont20: 0,
                  nominatedCont40: 0,
                  nominatedCont45: 0,
                  nominatedTeus: 0,
                };


                for (let rec of companyGroupedRecords) {
                  rec.groupType = 'TOTAL';
                  rec.label = rec.companyBranchCode;
                  rec.agentName = rec.companyBranchCode;
                  rec.agentCode = rec.companyBranchCode;
                  total.freehandCount += Number(rec.freehandCount) || 0;
                  total.nominatedCount += Number(rec.nominatedCount) || 0;
                  total.freehandCbm += Number(rec.freehandCbm) || 0;
                  total.freehandCw += Number(rec.freehandCw) || 0;
                  total.freehandCont20 += Number(rec.freehandCont20) || 0;
                  total.freehandCont40 += Number(rec.freehandCont40) || 0;
                  total.freehandCont45 += Number(rec.freehandCont45) || 0;
                  total.freehandTeus += Number(rec.freehandTeus) || 0;
                  total.nominatedCbm += Number(rec.nominatedCbm) || 0;
                  total.nominatedCw += Number(rec.nominatedCw) || 0;
                  total.nominatedCont20 += Number(rec.nominatedCont20) || 0;
                  total.nominatedCont40 += Number(rec.nominatedCont40) || 0;
                  total.nominatedCont45 += Number(rec.nominatedCont45) || 0;
                  total.nominatedTeus += Number(rec.nominatedTeus) || 0;
                }
                return [...companyGroupedRecords, total];
              }
            }
          }
        },
      },
    };
    return responsiveGridConfig(config);
  }

  onToggleShipmentType(dRecord: grid.DisplayRecord, shipmentType: 'FREE-HAND' | 'NOMINATED') {
    const { appContext, pageContext, reportFilter } = this.props;
    if (!reportFilter) return;
    let record: any = dRecord.record;

    const { dateFilter, partnerParams } = reportFilter;

    let plugin = new UIAgentTransactionsListPlugin()
      .withDateFilter('shipmentDate', dateFilter.fromValue, dateFilter.toValue)
      .withSearchPattern(partnerParams.searchPattern)
      .withParams('continent', partnerParams.continent)
      .withParams('source', partnerParams.source)
      .withParams('country', partnerParams.country)
      .withParams('fromLocationCode', partnerParams.fromLocationCode)
      .withParams('toLocationCode', partnerParams.toLocationCode)
      .withParams('shipmentType', shipmentType)

    const agentCode = record['agentCode'];
    if (['DAD', 'DHE', 'HAN', 'HCE', 'HCI', 'HCL', 'HCM', 'HPH', 'LSN'].includes(agentCode)) {
      plugin = plugin.withParams('companyBranch', agentCode);
    } else if (agentCode !== 'Total') {
      plugin = plugin.withParams('agentCode', agentCode);
    }

    appContext.createHttpBackendCall('PartnerReportService', 'searchAgentTransactionsReport', { params: plugin.getSearchParams() })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIVolumeSalemanKeyAccountReportList appContext={appCtx} pageContext={pageCtx}
              plugin={new entity.DbEntityListPlugin(data)} hideSubtotalColumns={true} />
          );
        }
        pageContext.createPopupPage(`view-all-${util.IDTracker.next()}`, "Shipment Details", createAppPage, { size: 'xl', backdrop: 'static' });
      })
      .call()
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    const { appContext, pageContext, reportFilter } = this.props;
    if (!reportFilter) return;
    let record: any = dRecord.record;

    const { dateFilter, partnerParams } = reportFilter;

    let plugin = new UIAgentTransactionsListPlugin()
      .withDateFilter('shipmentDate', dateFilter.fromValue, dateFilter.toValue)
      .withSearchPattern(partnerParams.searchPattern)
      .withParams('continent', partnerParams.continent)
      .withParams('source', partnerParams.source)
      .withParams('country', partnerParams.country)
      .withParams('fromLocationCode', partnerParams.fromLocationCode)
      .withParams('toLocationCode', partnerParams.toLocationCode)
      .withParams('shipmentType', partnerParams.shipmentType)

    const agentCode = record['agentCode'];
    if (['DAD', 'DHE', 'HAN', 'HCE', 'HCI', 'HCL', 'HCM', 'HPH', 'LSN'].includes(agentCode)) {
      plugin = plugin.withParams('companyBranch', agentCode);
    } else if (agentCode !== 'Total') {
      plugin = plugin.withParams('agentCode', agentCode);
    }

    appContext.createHttpBackendCall('PartnerReportService', 'searchAgentTransactionsReport', { params: plugin.getSearchParams() })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIVolumeSalemanKeyAccountReportList appContext={appCtx} pageContext={pageCtx}
              plugin={new entity.DbEntityListPlugin(data)} hideSubtotalColumns={true} />
          );
        }
        pageContext.createPopupPage(`view-all-${util.IDTracker.next()}`, `Shipment Details: ${record['label']}`, createAppPage, { size: 'xl', backdrop: 'static' });
      })
      .call()
  }
}