import React from 'react';
import * as FeatherIcon from "react-feather";

import { util, bs, input, entity } from '@datatp-ui/lib';
import { T } from '../../backend';
import { AddMethod } from 'app/crm/price';
import { ContainerType, ContainerTypeUnit } from '../../../common/ContainerTypeUtil';
import { ResponsiveGrid } from 'app/crm/common';
import { ContainerMarginConfig, DataContainerProcessor, UIContainerMarginListEditor } from './UIContainerMarginConfigList';

const { ZERO_AND_GREATER_VALIDATOR } = util.validator;

export interface UISimpleMarginConfigProps extends entity.AppDbComplexEntityProps {
  onApplyMargin: (quotation: any) => void;
  quotation: any;
}

interface UISimpleMarginConfigState {
  selectedContainerType: Record<string, boolean>;
}
export class UISimpleMarginConfig extends entity.AppDbComplexEntity<UISimpleMarginConfigProps, UISimpleMarginConfigState> {
  containerTypes: ContainerType[] = [];

  state: UISimpleMarginConfigState = {
    selectedContainerType: {},
  };

  constructor(props: UISimpleMarginConfigProps) {
    super(props);
    let { quotation } = this.props;
    let inquiry: any = quotation['inquiry'] || {};
    let containers: any[] = inquiry['containers'] || [];
    this.containerTypes = ContainerTypeUnit.computeFromContainer(containers);
  }

  componentDidMount() {
    // Initialize all charges as selected
    const selectedContainerType = this.containerTypes.reduce((acc, type) => {
      acc[type.name] = true;
      return acc;
    }, {} as Record<string, boolean>);
    this.setState({ selectedContainerType });
  }

  onToggleCharge = (name: string) => {
    const { observer } = this.props;
    let bean = observer.getMutableBean();

    this.setState(prevState => ({
      selectedContainerType: {
        ...prevState.selectedContainerType,
        [name]: !prevState.selectedContainerType[name]
      }
    }))
  }

  onSelectAll = (checked: boolean) => {
    const selectedContainerType = this.containerTypes.reduce((acc, type) => {
      acc[type.name] = checked;
      return acc;
    }, {} as Record<string, boolean>);
    this.setState({ selectedContainerType })
  }

  onModify = (bean: any, field: string, _oldVal: any, newVal: any) => {
    const { quotation } = this.props;

    if (field === 'addMethod') {
      bean[field] = newVal;
      bean['margin'] = 0;
    } else if (field === 'margin') {
      bean[field] = Number(newVal) || 0;
    } else {
      bean[field] = newVal;
    }

    if (field === 'margin' || field === 'roundStep') {
      const { selectedContainerType } = this.state;
      let marginVal = Number(bean.margin) || 0;
      let roundStep = Number(bean.roundStep) || 0;

      for (let record of quotation['quoteList']) {
        if (!record.priceGroup) record.priceGroup = {};

        for (let containerType of this.containerTypes) {
          if (selectedContainerType[containerType.name]) {
            let priceField: string | undefined = containerType.toFCLPriceLevel();
            if (priceField) {
              let refPriceField = `ref${priceField.charAt(0).toUpperCase() + priceField.slice(1)}`;
              let basePrice = parseFloat(record.priceGroup[refPriceField] || record['selling'] || 0);

              let newPrice: number;

              if (bean.addMethod === AddMethod.Amount) {
                // Thêm margin theo số tiền cố định
                newPrice = basePrice + marginVal;
              } else if (bean.addMethod === AddMethod.Percent) {
                // Thêm margin theo phần trăm
                newPrice = basePrice * (1 + marginVal / 100);

                // Áp dụng round step cho percent
                if (roundStep > 0) {
                  newPrice = Math.round(newPrice / roundStep) * roundStep;
                }
              } else {
                newPrice = basePrice;
              }

              record.priceGroup[priceField] = newPrice;
            }
          }
        }
      }
    }

    this.forceUpdate();
  }

  onReset = () => {
    const { selectedContainerType } = this.state;
    const { quotation } = this.props;
    for (let record of quotation['quoteList']) {
      if (!record.priceGroup) record.priceGroup = {};
      for (let containerType of this.containerTypes) {
        if (selectedContainerType[containerType.name]) {
          let priceField: string | undefined = containerType.toFCLPriceLevel();
          if (priceField) {
            let costFieldName = priceField ? `ref${priceField.charAt(0).toUpperCase() + priceField.slice(1)}` : '';
            let originCost: number = record.priceGroup[costFieldName] || 0;
            record.priceGroup[priceField] = originCost;
          }
        }
      }
    }
    this.forceUpdate();
  }

  onConfirm = () => {
    const { quotation, onApplyMargin } = this.props;
    onApplyMargin(quotation);
  }

  onMarginDataChange = (record: any, field: string, oldVal: any, newVal: any) => {
    const { quotation } = this.props;
    if (!quotation || !quotation.quoteList) return;
    const quoteId = record.quoteId;
    let priceField = record.priceField;
    let refPriceField = record.refPriceField;

    const quote = quotation.quoteList.find((q: any) => q.id === quoteId);
    if (!quote.priceGroup) quote.priceGroup = {};

    if (field === 'costing' && refPriceField) {
      quote.priceGroup[refPriceField] = parseFloat(newVal);
    } else if (field === 'selling' && priceField) {
      quote.priceGroup[priceField] = parseFloat(newVal);
    }
    this.forceUpdate();
  }

  render() {
    let { observer, readOnly, quotation, appContext, pageContext } = this.props;
    let bean = observer.getMutableBean();
    const { selectedContainerType } = this.state;
    let containerData: any[] = DataContainerProcessor.processGroupByCompany(quotation);

    return (
      <div className='flex-vbox'>
        <div className='flex-vbox p-1'>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBSelectField
                label={T('Charge Type')} bean={bean} field={'addMethod'} disable={readOnly}
                options={[AddMethod.Amount, AddMethod.Percent,]} optionLabels={[T('Amount'), T('Percent')]}
                onInputChange={(bean, field, oldVal, newVal) => this.forceUpdate()} />
            </bs.Col>
            <bs.Col span={3}>
              {bean.addMethod === AddMethod.Amount
                ? <input.BBCurrencyField
                  label={T('Margin')} bean={bean} field={'margin'} disable={readOnly} onInputChange={this.onModify} />
                : <input.BBPercentField label={T('Margin')}
                  bean={bean} field={'margin'} disable={readOnly} onInputChange={this.onModify} />
              }
            </bs.Col>
            <bs.Col span={3}>
              <input.BBCurrencyField
                label={T('Round Step')} bean={bean} field={'roundStep'}
                disable={readOnly || bean.addMethod === AddMethod.Amount}
                inputObserver={observer} validators={[ZERO_AND_GREATER_VALIDATOR]} onInputChange={this.onModify} />
            </bs.Col>
          </bs.Row>

          {this.containerTypes.length > 0 && (
            <>
              <div className="border rounded p-2 bg-white">
                <div className="d-flex align-items-center mb-1">
                  <FeatherIcon.Package size={14} className="text-primary me-1" />
                  <div className="fw-bold">Target Container Type</div>
                </div>

                <div className="row g-2">
                  {this.containerTypes.map((type, index) => (
                    <div key={index} className="col-md-4">
                      <div className="d-flex align-items-center">
                        <input.WCheckboxInput name={type.name} checked={selectedContainerType[type.name]}
                          onInputChange={() => this.onToggleCharge(type.name)} />
                        <span className="ms-1">{type.label}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {this.containerTypes.length > 0 && (
            <div className="flex-vbox justify-content-start align-items-center"
              style={{ minHeight: 300 }} key={util.IDTracker.next()}>
              <UIContainerMarginListEditor appContext={appContext} pageContext={pageContext} className="w-100 h-100"
                plugin={new entity.VGridEntityListEditorPlugin(containerData)} editorTitle='Container Margins'
                dialogEditor={true} onMarginDataChange={this.onMarginDataChange} />
            </div>
          )
          }
        </div>

        <div className='flex-hbox-grow-0 justify-content-end mt-1 border-top p-2'>

          <bs.Button laf='warning' outline className="px-2 py-1 mx-1" style={{ width: 150 }} onClick={this.onReset}>
            <FeatherIcon.GitPullRequest size={12} /> Reset to Origin
          </bs.Button>

          <bs.Button laf='info' outline className="px-2 py-1 mx-1" style={{ width: 100 }} onClick={this.onConfirm}>
            <FeatherIcon.Check size={12} /> OK
          </bs.Button>
        </div>

      </div>
    );
  }
}

export class UIMarginConfig extends entity.AppDbComplexEntity {

  render() {
    let { observer, readOnly, onModify } = this.props;
    let bean = observer.getMutableBean();

    return (
      <div className='flex-vbox p-1'>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBSelectField
              label={T('Charge Type')} bean={bean} field={'addMethod'} disable={readOnly}
              options={[AddMethod.Amount, AddMethod.Percent,]} optionLabels={[T('Amount'), T('Percent')]}
              onInputChange={(bean, field, oldVal, newVal) => this.forceUpdate()} />
          </bs.Col>
          <bs.Col span={3}>
            {bean.addMethod === AddMethod.Amount
              ? <input.BBCurrencyField label={T('Margin')} bean={bean} field={'margin'} disable={readOnly}
                inputObserver={observer} validators={[ZERO_AND_GREATER_VALIDATOR]} onInputChange={onModify} />
              : <input.BBPercentField label={T('Margin')}
                bean={bean} field={'margin'} disable={readOnly}
                inputObserver={observer} validators={[ZERO_AND_GREATER_VALIDATOR]}
                onInputChange={onModify} />
            }
          </bs.Col>
          <bs.Col span={3}>
            <input.BBCurrencyField
              label={T('Round Step')} bean={bean} field={'roundStep'}
              disable={readOnly || bean.addMethod === AddMethod.Amount}
              inputObserver={observer} validators={[ZERO_AND_GREATER_VALIDATOR]} onInputChange={onModify} />
          </bs.Col>
        </bs.Row>
      </div>
    );
  }

}

