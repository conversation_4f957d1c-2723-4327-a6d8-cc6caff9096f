import React from 'react';
import * as FeatherIcon from 'react-feather';
import { grid, input, entity, bs, util } from "@datatp-ui/lib";
import { isValidity, SalesQuoteFieldBuilder } from './QuotationFieldBuilder';
import { T } from '../../backend';
import { ContainerType, ContainerTypeUnit } from 'app/crm/common/ContainerTypeUtil';
import { buildTooltipValues } from 'app/crm/price';
import { GridColumn, GridConfig, ResponsiveGrid } from 'app/crm/common';

export function responsiveGridConfig(config: grid.VGridConfig, minWidth?: number): grid.VGridConfig {
  let fields: grid.FieldConfig[] = config.record.fields || [];

  const totalWidth = minWidth ? minWidth : window.innerWidth || 1920;
  const totalControlWidth = config.record.control ? config.record.control.width : 0;
  let totalWidthRemaining = totalWidth - totalControlWidth - fields.reduce((sum, field) => {
    const isVisible =
      !field.state ||
      !field.state.hasOwnProperty('visible') ||
      field.state.visible === true;
    return isVisible ? sum + (field.width || 0) : sum;
  }, 0);

  if (totalWidthRemaining > 0) {
    for (let i = 0; i < fields.length; i++) {
      if (fields[i].container === 'fixed-right') {
        fields[i].container = 'default';
      }
    }
  }
  config.record.fields = fields;
  return config;
}

export class GridConfigFactory {

  static createAirConfig(observer: entity.ComplexBeanObserver, uiList: entity.VGridEntityListEditor,): grid.FieldConfig[] {

    const { pageContext } = uiList.props;
    let writeCap = pageContext.hasUserWriteCapability();

    const onInputChange = (
      ctx: grid.FieldContext, oldVal: any, newVal: any) => {
      const { displayRecord } = ctx;
      if (oldVal !== newVal) {
        uiList.onSelect(displayRecord.row, displayRecord.record);
      }
    }

    let customFields: grid.FieldConfig[] = [
      {
        name: 'costing', label: 'Costing (KG)', width: 100,
        fieldDataGetter: (record: any) => {
          let priceGroup = record['priceGroup'] || {}
          let priceType: string = priceGroup['selectedPriceType'] || ''
          let refPriceType = `ref${priceType.charAt(0).toUpperCase() + priceType.slice(1)}`
          let val: any = priceGroup[refPriceType] || 0;
          let refPriceTypeNote = `${refPriceType}Note`;
          if (val === 0 && priceGroup[refPriceTypeNote]) return priceGroup[refPriceTypeNote];
          return val
        },
      },
      {
        name: 'selectedPrice', label: 'Selling', width: 150,
        fieldDataGetter: (record: any) => {
          let priceGroup = record['priceGroup'] || {}
          return priceGroup['selectedPrice'] || 0;
        },
        editor: {
          type: 'currency', enable: pageContext.hasUserWriteCapability(),
          renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
            const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
            let record = displayRecord.record;
            let priceGroup: any = record['priceGroup'] || {}
            if (!priceGroup[fieldConfig.name]) priceGroup[fieldConfig.name] = 0

            return (
              <input.BBCurrencyField bean={priceGroup} precision={3} style={{ height: '40px' }}
                field={fieldConfig.name} tabIndex={tabIndex} focus={focus} disable={isValidity(record)}
                onInputChange={(bean: any, _fieldName: string, _oldVal: any, _newVal: any) => onInputChange(record, 'priceGroup', null, bean)} />
            );
          },
          onInputChange: onInputChange
        },
      },
    ]

    return new SalesQuoteFieldBuilder(observer, onInputChange)
      .addBasicFields('Air')
      .addLocationFields(['Airport'], writeCap)
      .addCarrierField(writeCap)
      .addCurrencyField(writeCap)
      .addCustomFields(customFields)
      .addTransitTimeField(writeCap)
      .addCustomFields([
        {
          name: 'note', label: T('Notes'), width: 300, style: { height: '40px' },
          editor: {
            type: 'string', enable: writeCap, onInputChange: onInputChange,
            renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
              const { displayRecord, tabIndex, focus } = ctx;
              let record = displayRecord.record;
              return (
                <input.BBTextField style={{ height: '40px' }}
                  bean={record} field={'note'} tabIndex={tabIndex} focus={focus} onInputChange={_onInputChange} />
              );
            }
          },
        },
      ])
      .addValidityField(writeCap)
      .build();
  }

  static createFCLConfig(
    observer: entity.ComplexBeanObserver, uiList: entity.VGridEntityListEditor,
    containers: Array<ContainerType> = []): grid.FieldConfig[] {

    const onInputChange = (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
      if (oldVal !== newVal) {
        const { row, record } = ctx.displayRecord;
        uiList.onSelect(row, record)
      }
    }

    const { pageContext } = uiList.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let containerFields: grid.FieldConfig[] = []

    for (let container of containers) {
      let fieldName: string | undefined = container.toFCLPriceLevel();
      if (fieldName) {
        let fieldConfig: grid.FieldConfig = {
          name: fieldName, label: container.name, hint: container.label, width: 120, sortable: true,
          customHeaderRender(ctx: grid.VGridContext, field: grid.FieldConfig, headerEle: any) {
            let btnWidth = 20;
            const onSort = (field: grid.FieldConfig, desc: boolean) => {

              let sortFunc = function (rec1: any, rec2: any) {
                let val1 = 0;
                let val2 = 0;

                if (rec1) {
                  let priceGroup1 = rec1['priceGroup'] || {};
                  val1 = priceGroup1[field.name];
                }

                if (rec2) {
                  let priceGroup2 = rec2['priceGroup'] || {};
                  val2 = priceGroup2[field.name];
                }
                if (val1 === val2) return 0;
                let result = (val1 > val2) ? 1 : -1;
                if (desc) return result * -1;
                return result;
              };

              ctx.model.filteredRecords.sort(sortFunc);
              ctx.model.displayRecordList.updateRecords(ctx.model.filteredRecords);
              ctx.getVGrid().forceUpdateView();
            }

            return (
              <div className='flex-hbox justify-content-between'>
                <div className={'flex-hbox-grow-0 align-items-center'}>
                  <span className='text-secondary'>{`${field.label}`}</span>
                </div>
                <div className='flex-hbox-grow-0 gap-1' style={{ minWidth: btnWidth }}>
                  <button key='desc' className='btn btn-link' onClick={() => onSort(field, true)}>
                    <FeatherIcon.ChevronDown size={14} />
                  </button>
                  <button key='asc' className="btn btn-link" onClick={() => onSort(field, false)}>
                    <FeatherIcon.ChevronUp size={14} />
                  </button>
                </div>
              </div>
            )
          },
          fieldDataGetter: (record: any) => {
            let priceGroup = record['priceGroup'] || {}
            let val = priceGroup[fieldName || ''] || 0;
            return val
          },
          editor: {
            type: 'currency', enable: writeCap,
            renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
              const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
              let record = displayRecord.record;
              let priceGroup: any = record['priceGroup'] || {}
              let disabled: boolean = isValidity(record);

              return (
                <input.BBCurrencyField bean={priceGroup} precision={3} style={{ minHeight: record['rowHeight'] || 40 }}
                  field={fieldConfig.name} tabIndex={tabIndex} focus={focus} disable={disabled}
                  onInputChange={(bean: any, _fieldName: string, _oldVal: any, _newVal: any) => onInputChange(record, 'priceGroup', null, bean)} />
              );
            },
            onInputChange: onInputChange
          },
        }
        containerFields.push(fieldConfig)
      }
    }

    return new SalesQuoteFieldBuilder(observer, onInputChange)
      .addBasicFields('Sea')
      // .addLocationFields(['Port'], writeCap)
      .addCarrierField(writeCap)
      .addCurrencyField(writeCap)
      .addCustomFields(containerFields)
      .addTransitTimeField(writeCap)
      .addCustomFields([
        {
          name: 'freeTime', label: T('FreeTime'), width: 200,
          editor: {
            type: 'string', enable: true, onInputChange: onInputChange,
            renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
              const { displayRecord, tabIndex, focus } = ctx;
              let record = displayRecord.record
              return (
                <input.BBTextField style={{ height: '40px' }} disable={false}
                  bean={record} field={'freeTime'} tabIndex={tabIndex} focus={focus} onInputChange={_onInputChange} />
              )
            }
          }
        },
        {
          name: 'note', label: T('Remarks'), width: 420,
          editor: {
            type: 'string', enable: true, onInputChange: onInputChange,
            renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
              const { displayRecord, tabIndex, focus } = ctx;

              let record = displayRecord.record;
              let val = record['note'] || '';
              const htmlVal = val.split('\n').map((line: any, i: any) =>
                <div key={i}>{line}</div>
              );

              return (
                <bs.CssTooltip width={450} position='bottom-right'>
                  <bs.CssTooltipToggle>
                    <input.BBTextField style={{ height: '40px' }}
                      bean={record} field={'note'} tabIndex={tabIndex} focus={focus} onInputChange={_onInputChange} />
                  </bs.CssTooltipToggle>
                  <bs.CssTooltipContent className="flex-vbox p-2 rounded" >
                    <div className="tooltip-body">
                      {htmlVal}
                    </div>
                  </bs.CssTooltipContent>
                </bs.CssTooltip>
              );
            }
          }
        },
      ])
      .addValidityField(writeCap)
      .addCustomFields([
        {
          name: 'referenceCode', label: T('Cost Ref'), width: 60, container: 'fixed-right',
          style: { fontSize: '0.8rem', fontWeight: 'bold' }, cssClass: 'text-muted',
          customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {

            let record = dRecord.record;
            let priceGroup: any = record['priceGroup'] || {}

            // Lọc các key trong priceGroup bắt đầu bằng ref và có value > 0, bỏ qua refCommission
            const refKeys = Object.keys(priceGroup)
              .filter(key => key.startsWith('ref') &&
                !key.startsWith('refCommission') &&
                typeof priceGroup[key] === 'number' &&
                priceGroup[key] > 0)
              .map(key => {
                // Lấy tên key không có tiền tố "ref"
                const baseKey = key.substring(3); // Bỏ "ref" ở đầu
                const baseKeyFirstLower = baseKey.charAt(0).toLowerCase() + baseKey.slice(1);
                const displayName = ContainerTypeUnit.getDisplayNameFromFCLLevel(baseKeyFirstLower);

                // Đảm bảo baseKey tồn tại trong priceGroup
                if (priceGroup[baseKeyFirstLower] === undefined) {
                  priceGroup[baseKeyFirstLower] = 0;
                }

                // Tính profit và margin
                const refValue = priceGroup[key] || 0;
                const value = priceGroup[baseKeyFirstLower] || 0;
                const profit = value - refValue;
                // Tính margin đúng: (Profit / Cost) * 100
                const margin = refValue > 0 ? ((profit / refValue) * 100).toFixed(1) : 0;

                return {
                  key,
                  displayName,
                  baseKey: baseKeyFirstLower,
                  refValue,
                  value,
                  profit,
                  margin
                };
              });

            // Hàm xử lý copy dạng bảng vào clipboard
            const handleCopyToClipboard = () => {
              // Lọc chỉ những item có sell > 0
              const filteredKeys = refKeys.filter(item => item.value > 0);

              if (filteredKeys.length === 0) {
                bs.toastShow('No prices with sell value to copy!', { type: 'warning' });
                return;
              }

              // Format data để giống với bảng hiển thị
              const headers = ['Type', 'Cost', 'Sell', 'Profit', 'Margin'];

              // Create HTML table giống với bảng hiển thị
              const htmlData = `
                <table border="1" style="border-collapse: collapse; width: 100%;">
                  <thead>
                    <tr>
                      ${headers.map(header =>
                `<th style="border: 1px solid #000; padding: 6px; text-align: center; background-color: #f0f0f0;">${header}</th>`
              ).join('')}
                    </tr>
                  </thead>
                  <tbody>
                    ${filteredKeys.map(item => `
                        <tr>
                          <td style="border: 1px solid #000; padding: 6px; font-weight: bold;">${item.displayName}</td>
                          <td style="border: 1px solid #000; padding: 6px; text-align: right; color: #198754;">${item.refValue}</td>
                          <td style="border: 1px solid #000; padding: 6px; text-align: right; color: #0d6efd;">${item.value}</td>
                          <td style="border: 1px solid #000; padding: 6px; text-align: right; color: ${item.profit >= 0 ? '#198754' : '#dc3545'};">${item.profit}</td>
                          <td style="border: 1px solid #000; padding: 6px; text-align: right; color: ${Number(item.margin) >= 0 ? '#198754' : '#dc3545'};">${item.margin}%</td>
                        </tr>
                      `).join('')}
                  </tbody>
                </table>
                `;

              // Create plain text version for clipboard
              const plainTextData = `${headers.join('\t')}\n${filteredKeys.map(item =>
                `${item.displayName}\t${item.refValue}\t${item.value}\t${item.profit}\t${item.margin}%`
              ).join('\n')
                }`;

              // Create a temporary element to hold the HTML data
              const tempElement = document.createElement('div');
              tempElement.innerHTML = htmlData;

              // Copy to clipboard with HTML format
              const clipboardData = new ClipboardItem({
                'text/plain': new Blob([plainTextData], { type: 'text/plain' }),
                'text/html': new Blob([tempElement.innerHTML], { type: 'text/html' })
              });

              navigator.clipboard.write([clipboardData]).then(() => {
                bs.toastShow('Copied to clipboard!', { type: 'success' });
              });
            };

            if (!priceGroup[_field.name]) priceGroup[_field.name] = 0;
            const gridConfig: GridConfig = {
              header: {
                height: 20,
              },
              row: {
                height: 20,
              },
              showHeader: true,
              showBorder: true,
              columns: [
                { field: 'displayName', label: 'Type', cssClass: 'border-start border-light text-start fw-bold' },
                { field: 'refValue', label: 'Cost', cssClass: 'border-start border-light text-start text-success' },
                { field: 'value', label: 'Sell', cssClass: 'border-start border-light text-start text-primary' },
                {
                  field: 'profit', label: 'Profit',
                  cellRenderer(value: any, record: any, _column: GridColumn) {
                    const selling = record['value'];
                    const borderClass = value >= 0 ? 'text-success' : 'text-danger';
                    return (
                      <div className={`flex-hbox align-items-center justify-content-start border-start border-light ${borderClass} mx-0 text-start ps-1`} style={{ width: 80 }}>
                        {selling > 0 ? value : '-'}
                      </div>
                    )
                  },

                },
                {
                  field: 'margin', label: 'Margin',
                  cellRenderer(value: any, record: any, _column: GridColumn) {
                    const selling = record['value'];
                    const borderClass = value >= 0 ? 'text-success' : 'text-danger';
                    return (
                      <div className={`flex-hbox align-items-center justify-content-start border-start border-light ${borderClass} mx-0 text-start ps-1`} style={{ width: 80 }}>
                        {selling > 0 ? `${value}%` : '-'}
                      </div>
                    )
                  },
                },
              ],
              widthConfig: {
                totalWidth: 400,
                minColumnWidth: 80,
                ratios: [1, 1, 1, 1, 1]
              }
            }

            return (
              <bs.CssTooltip width={400} position='top-left' offset={{ x: -350, y: 10 }}>
                <bs.CssTooltipToggle>
                  <div className='flex-hbox flex-grow-0 text-muted fw-bold'>
                    <FeatherIcon.MoreHorizontal size={14} className="me-2" />
                  </div>
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent className="flex-vbox rounded" >
                  {refKeys.length > 0 &&
                    <div className='flex-vbox' onClick={handleCopyToClipboard}>
                      <ResponsiveGrid className="w-100 h-100" config={gridConfig} data={refKeys} />
                    </div>
                  }
                </bs.CssTooltipContent>
              </bs.CssTooltip>

            );
          }
        },
      ])
      .build();
  }

  static createLCLConfig(observer: entity.ComplexBeanObserver, uiList: entity.VGridEntityListEditor): grid.FieldConfig[] {

    const { pageContext } = uiList.props;
    let writeCap = pageContext.hasUserWriteCapability();

    const onInputChange = (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
      const { row, record } = ctx.displayRecord;
      if (oldVal !== newVal) {
        uiList.onSelect(row, record)
      }
    }

    let customFields: grid.FieldConfig[] = [
      {
        name: 'costing', label: 'Costing', width: 80,
        fieldDataGetter: (record: any) => {
          let priceGroup = record['priceGroup'] || {}
          let priceType: string = priceGroup['selectedPriceType'] || ''
          let refPriceType = `ref${priceType.charAt(0).toUpperCase() + priceType.slice(1)}`
          let val: any = priceGroup[refPriceType] || 0;
          let refPriceTypeNote = `${refPriceType}Note`;
          if (val === 0 && priceGroup[refPriceTypeNote]) return priceGroup[refPriceTypeNote];
          return val
        },
      },
      {
        name: 'selectedPrice', label: 'Selling', width: 120,
        fieldDataGetter: (record: any) => {
          let priceGroup = record['priceGroup'] || {}
          return priceGroup['selectedPrice'] || 0;
        },
        editor: {
          type: 'currency', enable: writeCap,
          renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
            const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
            let record = displayRecord.record;
            let priceGroup: any = record['priceGroup'] || {}
            if (!priceGroup[fieldConfig.name]) priceGroup[fieldConfig.name] = 0
            return (
              <input.BBCurrencyField bean={priceGroup} precision={3} style={{ height: '40px' }}
                field={fieldConfig.name} tabIndex={tabIndex} focus={focus}
                onInputChange={(bean: any, _fieldName: string, _oldVal: any, _newVal: any) => onInputChange(record, 'priceGroup', null, bean)} />
            );
          },
          onInputChange: onInputChange
        },
      }
    ]

    return new SalesQuoteFieldBuilder(observer, onInputChange)
      .addBasicFields('Sea')
      .addCarrierField(writeCap)
      // .addLocationFields(['Port'], writeCap)
      .addCurrencyField(writeCap)
      .addCustomFields(customFields)
      // .addFrequencyField(writeCap)
      .addTransitTimeField(writeCap)
      // .addTransitPortField(writeCap)
      // .addAgentField(writeCap)
      // .addPricingCreatorField()
      .addValidityField(writeCap)
      .addCustomFields([
        {
          name: 'note', label: T('Remarks'), width: 420,
          computeCssClasses(_ctx, dRecord) {
            let cssCustom = ''
            if (dRecord.getRecordState().isMarkModified()) cssCustom += ' text-warning fw-bold'
            return cssCustom;
          },
          editor: {
            type: 'string', enable: writeCap, onInputChange: onInputChange,
            renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
              const { displayRecord, tabIndex, focus } = ctx;
              let record = displayRecord.record;
              return (
                <bs.CssTooltip position='bottom-right'>
                  <bs.CssTooltipToggle>
                    <input.BBTextField style={{ height: '40px' }}
                      bean={record} field={'note'} tabIndex={tabIndex} focus={focus} onInputChange={_onInputChange} />
                  </bs.CssTooltipToggle>
                  <bs.CssTooltipContent className="d-flex flex-column p-2 rounded" >
                    <div className='rounded'>
                      {record['note'] || ''}
                    </div>
                  </bs.CssTooltipContent>
                </bs.CssTooltip>

              );
            }
          }
        },
      ])
      .build();
  }

}