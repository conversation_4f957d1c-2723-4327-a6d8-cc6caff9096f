import React from "react";
import * as FeatherIcon from "react-feather";

import { bs, grid, util, entity } from "@datatp-ui/lib";
import { T } from "../backend";

const TimeUtil = util.TimeUtil;

export interface Template {
  modelName: string;
  fileName: string;
  fields: Array<{ name: string; label: string; dataType?: string }>;
  records: any[];
}

export class TemplateFactory {
  static createTemplate(type: string): Template {
    switch (type) {
      case 'FCL_IMPORT':
        return {
          modelName: 'Import_FCL_Template',
          fileName: 'Import_FCL.xlsx',
          fields: [
            { name: "fromLocationCode", label: "POL", dataType: 'string' },
            { name: "toLocationCode", label: "POD", dataType: 'string' },
            { name: "carrierLabel", label: "Carrier", dataType: 'string' },
            { name: "dry20Price", label: "20DC", dataType: 'currency' },
            { name: "dry40Price", label: "40DC", dataType: 'currency' },
            { name: "highCube40Price", label: "40HC", dataType: 'currency' },
            { name: "surchargeNote", label: "Surcharge", dataType: 'string' },
            { name: "addCharge:TLX:SHIPMENT", label: "Telex /SWB", dataType: 'string' },
            { name: "cutoff", label: "Cut off time", dataType: 'string' },
            { name: "etd", label: "ETD", dataType: 'string' },
            { name: "transitTime", label: "T/T (days)", dataType: 'string' },
            { name: "transitPort", label: "Transit Port", dataType: 'string' },
            { name: "freeTime", label: "Freetime", dataType: 'string' },
            { name: "validFrom", label: "Valid From", dataType: 'date' },
            { name: "validTo", label: "Valid To", dataType: 'date' },
            { name: "handlingAgentPartnerLabel", label: "Agent", dataType: 'string' },
            { name: "note", label: "Note", dataType: 'string' },
            { name: "finalTerminalLocationLabel", label: "Terminal at POD", dataType: 'string' },
          ],
          records: [],
        };
      case 'FCL_EXPORT_NONE_US':
        return {
          modelName: 'Export_FCL_NONE_US_Template',
          fileName: 'Export_NONE_US_FCL.xlsx',
          fields: [
            { name: "carrierLabel", label: "Carrier", dataType: 'string' },
            { name: "fromLocationCode", label: "POL", dataType: 'string' },
            { name: "toLocationCode", label: "POD", dataType: 'string' },
            { name: "commodity", label: "Commodity", dataType: 'string' },
            { name: "currency", label: "Currency", dataType: 'string' },
            { name: "dry20Price", label: "20DC", dataType: 'currency' },
            { name: "dry40Price", label: "40DC", dataType: 'currency' },
            { name: "highCube40Price", label: "40HC", dataType: 'currency' },
            { name: "commissionDC20", label: "20DC (KB)", dataType: "currency" },
            { name: "commissionDC40", label: "40DC (KB)", dataType: "currency" },
            { name: "commissionHC40", label: "40HC (KB)", dataType: "currency" },
            { name: "frequency", label: "Frequency", dataType: 'string' },
            { name: "transitTime", label: "T/T (days)", dataType: 'string' },
            { name: "transitPort", label: "Transit Port", dataType: 'string' },
            { name: "freeTime", label: "Freetime", dataType: 'string' },
            { name: "remarks", label: "Remarks", dataType: 'string' },
            { name: "validFrom", label: "Valid From", dataType: 'date' },
            { name: "validTo", label: "Valid To", dataType: 'date' },
            { name: "note", label: "Note", dataType: 'string' },
          ],
          records: [],
        };
      case 'FCL_EXPORT_US_ROUTE':
        return {
          modelName: 'Export_FCL_US_ROUTE_Template',
          fileName: 'Export_US_ROUTE_FCL.xlsx',
          fields: [
            { name: "carrierLabel", label: "Carrier", dataType: 'string' },
            { name: "fromLocationCode", label: "POL", dataType: 'string' },
            { name: "toLocationCode", label: "POD", dataType: 'string' },
            { name: "finalTerminalLocationLabel", label: "Final Destination", dataType: 'string' },
            { name: "commodity", label: "Commodity", dataType: 'string' },
            { name: "currency", label: "Currency", dataType: 'string' },
            { name: "dry20Price", label: "20DC", dataType: 'currency' },
            { name: "dry40Price", label: "40DC", dataType: 'currency' },
            { name: "highCube40Price", label: "40HC", dataType: 'currency' },
            { name: "highCube45Price", label: "45HC", dataType: 'currency' },
            { name: "nor40Price", label: "40NOR", dataType: 'currency' },
            { name: "commissionDC20", label: "20DC (KB)", dataType: "currency" },
            { name: "commissionDC40", label: "40DC (KB)", dataType: "currency" },
            { name: "commissionHC40", label: "40HC (KB)", dataType: "currency" },
            { name: "commissionHC45", label: "45HC (KB)", dataType: "currency" },
            { name: "commission40NOR", label: "40NOR (KB)", dataType: "currency" },

            { name: "frequency", label: "Frequency", dataType: 'string' },
            { name: "transitTime", label: "T/T (days)", dataType: 'string' },
            { name: "transitPort", label: "Transit Port", dataType: 'string' },
            { name: "containerHandlingType", label: "Mode", dataType: 'string' },
            { name: "freeTime", label: "Freetime", dataType: 'string' },
            { name: "remarks", label: "Remarks", dataType: 'string' },
            { name: "validFrom", label: "Valid From", dataType: 'date' },
            { name: "validTo", label: "Valid To", dataType: 'date' },
            { name: "note", label: "Note", dataType: 'string' },
          ],
          records: [],
        };
      case 'FCL_EXPORT_SPECIAL':
        return {
          modelName: 'Export_FCL_SPECIAL_Template',
          fileName: 'Export_SPECIAL_FCL.xlsx',
          fields: [
            { name: "carrierLabel", label: "Carrier", dataType: 'string' },
            { name: "fromLocationCode", label: "POL", dataType: 'string' },
            { name: "toLocationCode", label: "POD", dataType: 'string' },
            { name: "finalTerminalLocationLabel", label: "Final Destination", dataType: 'string' },
            { name: "commodity", label: "Commodity", dataType: 'string' },
            { name: "currency", label: "Currency", dataType: 'string' },
            { name: "reefer20Price", label: "20RF", dataType: 'currency' },
            { name: "reefer40Price", label: "40RF", dataType: 'currency' },
            { name: "commissionRF20", label: "20RF (KB)", dataType: "currency" },
            { name: "commissionRF40", label: "40RF (KB)", dataType: "currency" },
            { name: "frequency", label: "Frequency", dataType: 'string' },
            { name: "transitTime", label: "T/T (days)", dataType: 'string' },
            { name: "transitPort", label: "Transit Port", dataType: 'string' },
            { name: "containerHandlingType", label: "Mode", dataType: 'string' },
            { name: "freeTime", label: "Freetime", dataType: 'string' },
            { name: "remarks", label: "Remarks", dataType: 'string' },
            { name: "validFrom", label: "Valid From", dataType: 'date' },
            { name: "validTo", label: "Valid To", dataType: 'date' },
            { name: "note", label: "Note", dataType: 'string' },
          ],
          records: [],
        };
      case 'LCL_EXPORT':
        return {
          modelName: 'Export_LCL_Template',
          fileName: 'Export_LCL.xlsx',
          fields: [
            { name: "fromLocationCode", label: "POL Code", dataType: 'string' },
            { name: "toLocationCode", label: "POD Code", dataType: 'string' },
            { name: "freightChargeLCL", label: "Freight (CBM)", dataType: "currency" },
            { name: "addCharge:CFS:CBM", label: "CFS (USD/CBM)" },
            { name: "addCharge:THC:CBM", label: "THC (USD/CBM)" },
            { name: "addCharge:EBS:CBM", label: "EBS (USD/CBM)" },
            { name: "addCharge:LSS:CBM", label: "LSS (USD/CBM)" },
            { name: "addCharge:RR:CBM", label: "RR (USD/CBM)" },
            { name: "addCharge:GRI:CBM", label: "GRI (USD/CBM)" },
            { name: "addCharge:DDC:CBM", label: "DDC (USD/CBM)" },
            { name: "addCharge:B_AFR:SET", label: "AFR/AMS/ISF (USD/SET)" },
            { name: "addCharge:BILL:SET", label: "BILL (USD/SET)" },
            { name: "frequency", label: "Frequency", dataType: 'string' },
            { name: "transitTime", label: "T/T (days)", dataType: 'string' },
            { name: "transitPort", label: "Via", dataType: 'string' },
            { name: "stuffingNote", label: "Stuffing", dataType: 'string' },
            { name: "validFrom", label: "Valid From", dataType: 'date' },
            { name: "validTo", label: "Valid To", dataType: 'date' },
            { name: "note", label: "Note", dataType: 'string' },
            { name: "localChargeAtDest", label: "LCC DEST", dataType: 'string' },
          ],
          records: [],
        };
      case 'LCL_IMPORT':
        return {
          modelName: 'Import_LCL_Template',
          fileName: 'Import_LCL.xlsx',
          fields: [
            { name: "fromLocationCode", label: "POL Code", dataType: 'string' },
            { name: "toLocationCode", label: "POD Code", dataType: 'string' },
            { name: "handlingAgentPartnerLabel", label: "Agent", dataType: 'string' },
            { name: "freightChargeLCL", label: "Freight (CBM)", dataType: "currency" },
            { name: "addCharge:DO:SET", label: "D/O (USD/BL)" },
            { name: "addCharge:CFS:CBM", label: "CFS (USD/CBM)" },
            { name: "addCharge:THC:CBM", label: "THC (USD/CBM)" },
            { name: "addCharge:CIC:CBM", label: "CIC (USD/CBM)" },
            { name: "cutoff", label: "Cut off time", dataType: 'string' },
            { name: "frequency", label: "Frequency", dataType: 'string' },
            { name: "transitTime", label: "T/T (days)", dataType: 'string' },
            { name: "transitPort", label: "Via", dataType: 'string' },
            { name: "surchargeNote", label: "Surcharge", dataType: 'string' },
            { name: "note", label: "Note", dataType: 'string' },
            { name: "validFrom", label: "Valid From", dataType: 'date' },
            { name: "validTo", label: "Valid To", dataType: 'date' },
          ],
          records: [],
        };
      case 'LCL_IMPORT_CHINA':
        return {
          modelName: 'Import_LCL_Template',
          fileName: 'Import_LCL_CHINA.xlsx',
          fields: [
            { name: "fromLocationCode", label: "POL Code", dataType: 'string' },
            { name: "toLocationCode", label: "POD Code", dataType: 'string' },
            { name: "handlingAgentPartnerLabel", label: "Agent", dataType: 'string' },
            { name: "minimumChargeLCL", label: "Minimum (< 1 CBM)", dataType: "currency" },
            { name: "less2CbmPrice", label: "< 2 CBM", dataType: "currency" },
            { name: "less3CbmPrice", label: "< 3 CBM", dataType: "currency" },
            { name: "less5CbmPrice", label: "< 5 CBM", dataType: "currency" },
            { name: "less7CbmPrice", label: "< 7 CBM", dataType: "currency" },
            { name: "less10CbmPrice", label: "< 10 CBM", dataType: "currency" },
            { name: "geq10CbmPrice", label: "< 15 CBM", dataType: "currency" },
            { name: "addCharge:DO:SET", label: "D/O (USD/BL)" },
            { name: "addCharge:CFS:CBM", label: "CFS (USD/CBM)" },
            { name: "addCharge:THC:CBM", label: "THC (USD/CBM)" },
            { name: "addCharge:CIC:CBM", label: "CIC (USD/CBM)" },
            { name: "cutoff", label: "Cut off time", dataType: 'string' },
            { name: "frequency", label: "Frequency", dataType: 'string' },
            { name: "transitTime", label: "T/T (days)", dataType: 'string' },
            { name: "transitPort", label: "Via", dataType: 'string' },
            { name: "surchargeNote", label: "Surcharge", dataType: 'string' },
            { name: "note", label: "Note", dataType: 'string' },
            { name: "validFrom", label: "Valid From", dataType: 'date' },
            { name: "validTo", label: "Valid To", dataType: 'date' },
          ],
          records: [],
        };
      case 'AIR_EXPORT_TEMPLATE':
        return {
          modelName: AIR_EXPORT_TEMPLATE.modelName,
          fileName: AIR_EXPORT_TEMPLATE.fileName,
          fields: AIR_EXPORT_TEMPLATE.fields,
          records: [],
        };
      case 'AIR_IMPORT_TEMPLATE':
        return {
          modelName: AIR_IMPORT_TEMPLATE.modelName,
          fileName: AIR_IMPORT_TEMPLATE.fileName,
          fields: AIR_IMPORT_TEMPLATE.fields,
          records: [],
        };
      default:
        throw new Error(`Unknown template type: ${type}`);
    }
  }
}

export const AIR_EXPORT_TEMPLATE: any = {
  modelName: 'Export_Air_Template',
  fileName: 'Export_Air.xlsx',
  fields: [
    { name: "fromLocationCode", label: "AOL Code", dataType: 'string' },
    { name: "toLocationCode", label: "AOD Code", dataType: 'string' },
    { name: "carrierLabel", label: "Airline", dataType: 'string' },

    { name: "commodity", label: "Commodity", dataType: 'string' },
    { name: "packingNote", label: "CTNS/ PALLET", dataType: 'string' },

    { name: "minPrice", label: "Minimum", dataType: "currency" },
    { name: "level1Price", label: "-45(KG)", dataType: "currency" },
    { name: "level2Price", label: "+45(KG)", dataType: "currency" },
    { name: "level3Price", label: "+100(KG)", dataType: "currency" },
    { name: "level4Price", label: "+250(KG)", dataType: "currency" },
    { name: "level5Price", label: "+300(KG)", dataType: "currency" },
    { name: "level6Price", label: "+500(KG)", dataType: "currency" },
    { name: "level7Price", label: "+1000(KG)", dataType: "currency" },

    { name: "addCharge:FSC:KGM", label: "FSC (USD/KGS)" },
    { name: "addCharge:SCC:KGM", label: "SCC (USD/KGS)" },
    { name: "addCharge:AMS:HAWB", label: "AMS (USD/HAWB)" },
    { name: "addCharge:AMS:MAWB", label: "AMS (USD/MAWB)" },
    { name: "addCharge:AWB:SET", label: "AWB (USD/SET)" },
    { name: "addCharge:XRAY:KGM", label: "XRAY (USD/KGS)" },
    { name: "addCharge:TCS:KGM", label: "TCS (USD/KGS)" },
    { name: "addCharge:FHL:SET", label: "FHL (USD/SET)" },
    { name: "addCharge:FWB:SET", label: "FWB (USD/SET)" },
    { name: "addCharge:CCA:SET", label: "CCA (USD/SET)" },
    { name: "addCharge:DG:SET", label: "DG (USD/SET)" },

    { name: "frequency", label: "Frequency", dataType: 'string' },
    { name: "transitTime", label: "T/T (days)", dataType: 'string' },
    { name: "transitPort", label: "Transit Port", dataType: 'string' },

    { name: "flightNote", label: "Flight No.", dataType: 'string' },

    { name: "note", label: "Note", dataType: 'string' },
    { name: "validFrom", label: "Valid From", dataType: 'date' },
    { name: "validTo", label: "Valid To", dataType: 'date' },
  ],
  records: [],
}

export const AIR_IMPORT_TEMPLATE: any = {
  modelName: 'Import_Air_Template',
  fileName: 'Import_Air.xlsx',
  fields: [
    { name: "fromLocationCode", label: "AOL Code", dataType: 'string' },
    { name: "toLocationCode", label: "AOD Code", dataType: 'string' },
    { name: "handlingAgentPartnerLabel", label: "Agent", dataType: 'string' },
    { name: "commodity", label: "Commodity", dataType: 'string' },
    { name: "packingNote", label: "CTNS/ PALLET", dataType: 'string' },

    { name: "carrierLabel", label: "Airline", dataType: 'string' },

    { name: "minPrice", label: "Minimum", dataType: "currency" },
    { name: "level1Price", label: "-45(KG)", dataType: "currency" },
    { name: "level2Price", label: "+45(KG)", dataType: "currency" },
    { name: "level3Price", label: "+100(KG)", dataType: "currency" },
    { name: "level4Price", label: "+250(KG)", dataType: "currency" },
    { name: "level5Price", label: "+300(KG)", dataType: "currency" },
    { name: "level6Price", label: "+500(KG)", dataType: "currency" },
    { name: "level7Price", label: "+1000(KG)", dataType: "currency" },

    { name: "addCharge:FSC:KGM", label: "FSC (USD/KGS)" },
    { name: "addCharge:SCC:KGM", label: "SCC (USD/KGS)" },
    { name: "addCharge:AMS:HAWB", label: "AMS (USD/HAWB)" },
    { name: "addCharge:AMS:MAWB", label: "AMS (USD/MAWB)" },
    { name: "addCharge:AWB:SET", label: "AWB (USD/SET)" },
    { name: "addCharge:XRAY:KGM", label: "XRAY (USD/KGS)" },
    { name: "addCharge:TCS:KGM", label: "TCS (USD/KGS)" },
    { name: "addCharge:FHL:SET", label: "FHL (USD/SET)" },
    { name: "addCharge:FWB:SET", label: "FWB (USD/SET)" },
    { name: "addCharge:CCA:SET", label: "CCA (USD/SET)" },
    { name: "addCharge:DG:SET", label: "DG (USD/SET)" },

    { name: "frequency", label: "Frequency", dataType: 'string' },
    { name: "transitTime", label: "T/T (days)", dataType: 'string' },
    { name: "transitPort", label: "Transit Port", dataType: 'string' },

    { name: "flightNote", label: "Flight No.", dataType: 'string' },
    { name: "note", label: "Note", dataType: 'string' },
    { name: "validFrom", label: "Valid From", dataType: 'date' },
    { name: "validTo", label: "Valid To", dataType: 'date' },
  ],
  records: [],
}


export const AIR_TEMPLATE: Template[] = [
  TemplateFactory.createTemplate('AIR_EXPORT_TEMPLATE'),
  TemplateFactory.createTemplate('AIR_IMPORT_TEMPLATE'),
]

export const FCL_TEMPLATES: Template[] = [
  TemplateFactory.createTemplate('FCL_IMPORT'),
  TemplateFactory.createTemplate('FCL_EXPORT_NONE_US'),
  TemplateFactory.createTemplate('FCL_EXPORT_US_ROUTE'),
  TemplateFactory.createTemplate('FCL_EXPORT_SPECIAL'),
]

export const LCL_TEMPLATES: Template[] = [
  TemplateFactory.createTemplate('LCL_EXPORT'),
  TemplateFactory.createTemplate('LCL_IMPORT_CHINA'),
  TemplateFactory.createTemplate('LCL_IMPORT'),
]

export const TEMPLATE_MAP = {
  AIR: {
    EXPORT: TemplateFactory.createTemplate('AIR_EXPORT_TEMPLATE'),
    IMPORT: TemplateFactory.createTemplate('AIR_IMPORT_TEMPLATE')
  },

  FCL: {
    IMPORT: TemplateFactory.createTemplate('FCL_IMPORT'),
    EXPORT_NONE_US: TemplateFactory.createTemplate('FCL_EXPORT_NONE_US'),
    EXPORT_US_ROUTE: TemplateFactory.createTemplate('FCL_EXPORT_US_ROUTE'),
    EXPORT_SPECIAL: TemplateFactory.createTemplate('FCL_EXPORT_SPECIAL')
  },

  LCL: {
    EXPORT: TemplateFactory.createTemplate('LCL_EXPORT'),
    IMPORT_CHINA: TemplateFactory.createTemplate('LCL_IMPORT_CHINA'),
    IMPORT: TemplateFactory.createTemplate('LCL_IMPORT')
  }
}

interface UITransportChargeListExportButtonProps extends entity.XlsxExportButtonProps { }
export class UITransportChargeListExportButton extends entity.XlsxExportButton<UITransportChargeListExportButtonProps> {

  override initDataSelectModel = (model: grid.DataSelectModel) => {
    let excludeFields = [
      'sourceCode', 'sourceFrom', 'verify', "shareable",
      ...entity.DbEntityListConfigTool.FIELD_ENTITY.map(sel => sel.name)
    ]
    model.selectMode = 'selected'
    model.setFieldSelect(false, excludeFields);
  }

  override customDataListExportModel = (model: entity.DataListExportModel): entity.DataListExportModel => {
    let dateFormat: string = TimeUtil.toDateIdFormat(new Date())
    model.fileName = "ExportData_" + dateFormat + ".xlsx";
    model.modelName = 'ExportData'
    return model;
  }

  override render() {
    return (
      <bs.Button laf={'info'} outline onClick={this.onExportCustomization} style={{ width: '100px' }}>
        <FeatherIcon.Download size={12} /> {T("Export")}
      </bs.Button>
    )
  }

}

