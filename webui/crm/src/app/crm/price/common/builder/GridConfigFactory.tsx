import React from 'react';
import * as FeatherIcon from 'react-feather';

import { grid, input, app, bs, entity, util } from "@datatp-ui/lib";

import { UIPriceListConfigUtil } from "../UITransportChargeListUtils";
import { GroupType, T, TransportPurpose } from "../../backend";
import { FieldBuilder, TooltipField } from './FieldBuilder';
import { PriceListConfig } from '.';
import { PricePlugin, SearchParams } from './PluginBuilder';

import { UIPriceAnalysisReport } from '../../report/UIPriceReport';
import { ContainerType, ContainerTypeUnit } from '../../../common/ContainerTypeUtil';
import { BBRefBFSOnePartner } from '../../../partner';
import { TransportationMode, TransportationTool } from 'app/crm/common';
import { BBRefSubContactor } from 'app/crm/partner/BBRefSubcontractor';

const { formater } = util.text

const CELL_HEIGHT: number = 45;

export class GridConfigFactory {

  static createPriceField(types: ContainerType[]): { priceFields: grid.FieldConfig[], commissionFields: grid.FieldConfig[] } {
    let priceFields: grid.FieldConfig[] = [];
    let commissionFields: grid.FieldConfig[] = [];

    let tooltipFields: TooltipField[] = [
      { key: 'transitPort', label: 'Via' },
      { key: 'freeTime', label: 'Freetime' },
      { key: 'remarks', label: 'Remarks' },
      { key: 'note', label: 'Note' },
    ];

    for (let type of types) {
      let fieldName: string | undefined = type.toFCLPriceLevel();
      if (!fieldName) continue;

      let field: grid.FieldConfig = {
        name: fieldName, label: type.name, width: 100, sortable: true,
        fieldDataGetter: (record: any = {}) => {
          if (!record) record = {}
          let val = record[fieldName || ''] || 0;
          let fieldNoteName = fieldName + "Note";
          if (!val || val === 0) return record[fieldNoteName] || "-";
          return val;
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
          return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields);
        },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
      }

      priceFields.push(field);

      let commissionFieldName: string = `commission${fieldName.charAt(0).toUpperCase()}${fieldName.slice(1)}`;
      let commField: grid.FieldConfig = {
        name: commissionFieldName, label: type.name, width: 80,
        fieldDataGetter: (record: any = {}) => {
          if (!record) record = {}
          let val = record[commissionFieldName || ''] || 0;
          if (!val || val === 0) return '';
          return val;
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
          return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields);
        },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
      }
      commissionFields.push(commField)
    }
    return { priceFields, commissionFields };
  }

  static createPriceConfig(uiList: entity.DbEntityList): PriceListConfig {
    let { plugin } = uiList.props;
    let pluginImp = plugin as PricePlugin;

    let mode: TransportationMode = pluginImp.mode;
    let purpose: TransportPurpose = pluginImp.getPurpose() || 'IMPORT';
    let isCBT: boolean = pluginImp.isCBT()

    let priceConfig: PriceListConfig;

    if (TransportationTool.isSeaFCL(mode)) {
      if (!pluginImp.getGroupType()) pluginImp.withGroupType('NONE_US');
      priceConfig = purpose === 'IMPORT'
        ? GridConfigFactory.FCL_IMPORT_CONFIG(uiList)
        : GridConfigFactory.FCL_EXPORT_CONFIG(uiList, pluginImp.getGroupType())

    } else if (TransportationTool.isSeaLCL(mode)) {
      priceConfig = purpose === 'IMPORT'
        ? GridConfigFactory.LCL_IMPORT_CONFIG(uiList)
        : GridConfigFactory.LCL_EXPORT_CONFIG(uiList)

    } else if (TransportationTool.isAir(mode)) {
      priceConfig = GridConfigFactory.AIR_CONFIG(uiList)
    } else if (TransportationTool.isTruckRegular(mode)) {
      priceConfig = GridConfigFactory.TRUCK_CONFIG(uiList, isCBT)
    } else if (TransportationTool.isTruckContainer(mode)) {
      priceConfig = GridConfigFactory.CONTAINER_CONFIG(uiList, isCBT)
    } else {
      throw new Error("Unsupported transportation mode");
    }
    let enableEditor = !!pluginImp.toSearchParams().pricingCreatorId;
    priceConfig.fields.forEach(field => {
      if (field.editor) field.editor.enable = enableEditor;
    })

    return priceConfig;
  }


  static onInputChange = (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
    if (oldVal !== newVal) {
      ctx.displayRecord.getRecordState().markModified();
      ctx.gridContext.getVGrid().forceUpdateView();
    }
  }

  static AIR_CONFIG(uiList: entity.DbEntityList): PriceListConfig {

    let tooltipFields: TooltipField[] = [
      { key: 'note', label: 'Note' },
    ]

    let customFields: grid.FieldConfig[] = [
      {
        name: 'commodity', label: T(`Commodity`), width: 150,
        editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
          return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
        }
      },
      {
        name: 'packingNote', label: T(`CTNS/ PALLET`), width: 150,
        editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
          return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
        }
      },
    ];

    const fieldBuilder = new FieldBuilder(tooltipFields)
      .addBasicFields()
      .addLocationFields(['Airport'])
      .addCarrierField(uiList, "Airline")
      .addAgentField(uiList, "Agent")
      .addCustomFields(customFields)
      .addPriceField('minPrice', 'Min')
      .addPriceField('level1Price', '-45(KG)')
      .addPriceField('level2Price', '+45(KG)')
      .addPriceField('level3Price', '+100(KG)')
      .addPriceField('level4Price', '+250(KG)')
      .addPriceField('level5Price', '+300(KG)')
      .addPriceField('level6Price', '+500(KG)')
      .addPriceField('level7Price', '+1000(KG)')
      .addCurrencyField()
      .addLocalChargeField('FSC', 'KGM')
      .addLocalChargeField('SCC', 'KGM')
      .addLocalChargeField('AMS', 'HAWB')
      .addLocalChargeField('AMS', 'MAWB')
      .addLocalChargeField('AWB', 'SET')
      .addLocalChargeField('XRAY', 'KGM')
      .addLocalChargeField('TCS', 'KGM')
      .addLocalChargeField('FHL', 'SET')
      .addLocalChargeField('FWB', 'SET')
      .addLocalChargeField('CCA', 'SET')
      .addLocalChargeField('DG', 'SET')
      .addCustomFields([
        {
          name: 'roeVndUsd', label: T(`ROE (VND/USD)`), width: 180,
          editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
        },
        {
          name: 'flightNote', label: T(`Flight No.`), width: 150,
          editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
        },
      ])
      .addFrequencyField()
      .addTransitTimeField()
      .addTransitPortField()
      .addValidDateField()
      .addCustomFields([UIPriceListConfigUtil.FIELD_NOTES(uiList)])
      .addCustomFields([
        { name: 'createdBy', label: T('Creator.'), width: 120, filterable: true },
        {
          name: 'createdTime', label: T('Created Time'), width: 170,
          filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime
        },
        {
          name: 'modifiedTime', label: T('Modified Time'), width: 170,
          filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime
        },
      ])

    return {
      fields: fieldBuilder.build(),
      fieldGroups: fieldBuilder.fieldGroups,
    };
  }

  static FCL_EXPORT_CONFIG(uiList: entity.DbEntityList, groupType: GroupType = 'NONE_US'): PriceListConfig {

    let containerTypes: ContainerType[] = ContainerTypeUnit.FCL_GROUP[groupType];

    let { priceFields, commissionFields } = GridConfigFactory.createPriceField(containerTypes)
    let priceFieldNames: string[] = priceFields.map(sel => sel.name);
    let commissionFieldNames: string[] = commissionFields.map(sel => sel.name);

    let customFields: grid.FieldConfig[] = [
      {
        name: 'containerHandlingType', label: T(`Mode`), width: 150, state: { visible: groupType !== 'NONE_US' },
        editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
          return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
        }
      },
      {
        name: 'freeTime', label: T('Freetime'), width: 200,
        editor: { type: "string", onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
          return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
        }
      },
    ]

    let tooltipFields: TooltipField[] = [
      { key: 'transitPort', label: 'Via' },
      { key: 'freeTime', label: 'Freetime' },
      { key: 'remarks', label: 'Remarks' },
      { key: 'note', label: 'Note' },
    ]

    const handleClick = (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {

      let record: any = dRecord.record;
      // Define fields to copy
      let priceMapping = priceFields.map(field => {
        let fieldName = field.name;
        // let commissionFieldName: string = `commission${fieldName.charAt(0).toUpperCase()}${fieldName.slice(1)}`;
        // let comVal = record[commissionFieldName] || 0;
        let val = record[field.name] || 0
        return { name: field.name, label: field.label, value: (val) || 0 }
      })

      commissionFields.forEach(field => {
        let val = record[field.name] || 0
        priceMapping.push({ name: field.name, label: `${field.label} (KB)`, value: (val) || 0 })
      })


      const fields = [
        { name: 'carrierLabel', label: 'Carrier', value: record['carrierLabel'] || '' },
        { name: 'fromLocationLabel', label: 'Port of Loading', value: record['fromLocationLabel'] || '' },
        { name: 'toLocationLabel', label: 'Port of Discharge', value: record['toLocationLabel'] || '' },
        { name: 'currency', label: 'Currency', value: record['currency'] },
        ...priceMapping,
        { name: 'frequency', label: 'Frequency', value: record['frequency'] || '' },
        { name: 'transitTime', label: 'T/T', value: (record['transitTime'] || '') + ' ' + (record['transitPort'] || '') },
        { name: 'validTo', label: 'Valid Date', value: (record['validTo'] || '').substring(0, 10) },
        { name: 'freeTime', label: 'FreeTime', value: record['freeTime'] || '' },
        { name: 'note', label: 'Note', value: record['note'] || '' },
        { name: 'remarks', label: 'Remarks', value: record['remarks'] || '' },
      ];

      // Create header row with borders
      const headerRow = fields.map(f => f.label).join('\t');

      // Create value row with borders
      const valueRow = fields.map(f => {
        return f.value
      }).join('\t');

      // Combine with special characters for Excel-like borders
      const tableData = `${headerRow}\n${valueRow}`;

      // Copy to clipboard with HTML format for Excel
      const htmlData = `
        <table border="1" style="border-collapse: collapse">
          <tr>${fields.map(f => `<td style="border: 1px solid #000; padding: 4px">${f.label}</td>`).join('')}</tr>
          <tr>${fields.map(f => `<td style="border: 1px solid #000; padding: 4px">${f.value}</td>`).join('')}</tr>
        </table>
      `;

      // Create a temporary element to hold the HTML data
      const tempElement = document.createElement('div');
      tempElement.innerHTML = htmlData;

      // Copy to clipboard with HTML format
      const clipboardData = new ClipboardItem({
        'text/plain': new Blob([tableData], { type: 'text/plain' }),
        'text/html': new Blob([tempElement.innerHTML], { type: 'text/html' })
      });

      navigator.clipboard.write([clipboardData]).then(() => {
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      });
    };

    const fieldBuilder = new FieldBuilder(tooltipFields)
      .addBasicFields()
      .addCustomFields([
        {
          name: '_index_', label: '#', width: 40, container: 'fixed-left',
          removable: false, resizable: false,
          customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, record: grid.DisplayRecord, _focus: boolean) => {
            return (
              <bs.CssTooltip width={200} position='bottom-right' offset={{ x: 200, y: 0 }}>
                <bs.CssTooltipToggle>
                  <div className='flex-hbox justify-content-center align-items-center' onClick={() => handleClick(_ctx, _field, record)}>
                    {record.getDisplayRow()}
                    {/* <FeatherIcon.BarChart2 size={12} /> */}
                  </div>
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent className="d-flex flex-column rounded" >
                  <div className='rounded text-secondary fw-normal'>Click để copy theo dạng lưới.</div>
                </bs.CssTooltipContent>
              </bs.CssTooltip>
            )
          }
        },
      ])
      .addLocationFields(['Port'])
      .addCustomFields([
        {
          name: 'commodity', label: T('Commodity'), width: 220,
          filterable: true, filterableType: 'string',
          state: { visible: groupType === 'NONE_US' },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
            return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
          },
          editor: { type: "string", onInputChange: GridConfigFactory.onInputChange },
        },
        {
          name: 'finalTerminalLocationLabel', label: T('Final Destination'), width: 220,
          filterable: true, filterableType: 'string',
          state: { visible: groupType !== 'NONE_US' },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
            return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
          },
          editor: { type: "string", onInputChange: GridConfigFactory.onInputChange },
        },
      ])
      .addCarrierField(uiList, 'Carrier', 'fixed-left')
      .addCurrencyField()
      .addCustomFields([...priceFields, ...commissionFields])
      .addFrequencyField()
      .addTransitTimeField()
      .addTransitPortField()
      .addCustomFields(customFields)
      .addValidDateField()
      .addCustomFields([
        UIPriceListConfigUtil.FIELD_NOTES(uiList),
        {
          name: 'remarks', label: T('Remarks'), width: 250,
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
          editor: {
            type: "string",
            renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
              const { displayRecord: dRecord, fieldConfig: field, focus, tabIndex } = ctx;
              return (
                <div className='flex-hbox justify-content-between align-items-center' title={""}>
                  <input.BBTextField bean={dRecord.record} field={field.name} style={{ minHeight: CELL_HEIGHT, height: 'auto', width: '400px' }}
                    tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                </div>
              );
            },
            onInputChange: GridConfigFactory.onInputChange
          },
        },
        {
          name: 'createdBy', label: T('Creator.'), width: 120, filterable: true,
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
        {
          name: 'createdTime', label: T('Created Time'), width: 170,
          filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime
        },
        {
          name: 'modifiedTime', label: T('Modified Time'), width: 170,
          filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime
        },
        {
          name: 'groupType', label: T('Group'), width: 100,
          computeCssClasses(_ctx, _dRecord) {
            return 'fw-bold text-warning'
          },
        },
        {
          name: 'analysis', label: T(`Act.`), width: 40, container: 'fixed-right',
          customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
            const record = dRecord.record;
            let uiList = _ctx.uiRoot as entity.DbEntityList;
            const { pageContext } = uiList.props;

            const handleOnChange = () => {
              let searchParam: SearchParams = new SearchParams('EXPORT', TransportationMode.SEA_FCL);
              searchParam.fromLocationCode = (record['fromLocationCode'] || '').trim();
              searchParam.fromLocationLabel = record['fromLocationLabel']
              searchParam.toLocationCode = (record['toLocationCode'] || '').trim()
              searchParam.toLocationLabel = record['toLocationLabel']
              searchParam.carrierLabel = (record['carrierLabel'] || '').trim();
              const createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => (
                <UIPriceAnalysisReport initSearchParam={searchParam} appContext={appCtx} pageContext={pageCtx} sourceReportLine={record} groupType={groupType} />
              );
              pageContext.createPopupPage('price-trends-analysis', T('Price Trends Analysis'), createPageContent, { size: 'xl', backdrop: 'static' });
            }

            return (
              <bs.CssTooltip width={200} offset={{ x: -200, y: 0 }}>
                <bs.CssTooltipToggle>
                  <div className='flex-hbox justify-content-center align-items-center' >
                    <bs.Button laf='info' outline className='border-0 py-2 px-1 w-100' onClick={() => handleOnChange()}>
                      <FeatherIcon.BarChart2 size={12} />
                    </bs.Button>
                  </div>
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent className="d-flex flex-column rounded" >
                  <div className='rounded text-secondary fw-normal'>Xem phân tích/ biến động giá theo thời gian.</div>
                </bs.CssTooltipContent>
              </bs.CssTooltip>
            )
          }
        },

      ])

    fieldBuilder.addGroup('_OF', {
      label: 'O/F',
      fields: priceFieldNames
    })

    fieldBuilder.addGroup('_KB', {
      label: 'KB',
      fields: commissionFieldNames
    })

    return {
      fields: fieldBuilder.build(),
      fieldGroups: fieldBuilder.fieldGroups,
    };
  }

  static FCL_IMPORT_CONFIG(uiList: entity.DbEntityList): PriceListConfig {

    let tooltipFields: TooltipField[] = [
      { key: 'surchargeNote', label: 'Surcharge' },
      { key: 'etd', label: 'ETD' },
      { key: 'frequency', label: 'Frequency' },
      { key: 'transitTime', label: 'T/T (days)' },
      { key: 'transitPort', label: 'Via' },
      { key: 'freeTime', label: 'Freetime At POD' },
      { key: 'note', label: 'Note' },
    ]

    const handleClick = (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
      let record: any = dRecord.record;

      const fields = [
        { name: 'carrierLabel', label: 'Carrier', value: record['carrierLabel'] || '' },
        { name: 'fromLocationLabel', label: 'Port of Loading', value: record['fromLocationLabel'] || '' },
        { name: 'toLocationLabel', label: 'Port of Discharge', value: record['toLocationLabel'] || '' },
        { name: 'currency', label: 'Curr', value: record['currency'] },
        { name: 'dry20Price', label: '20DC', value: record['dry20Price'] || 0 },
        { name: 'dry40Price', label: '40DC', value: record['dry40Price'] || 0 },
        { name: 'highCube40Price', label: '40HC', value: record['highCube40Price'] || 0 },
        { name: 'transitTime', label: 'T/T', value: (record['transitTime'] || '') + ' ' + (record['transitPort'] || '') },
        { name: 'validTo', label: 'Valid Date', value: (record['validTo'] || '').substring(0, 10) },
        { name: 'freeTime', label: 'Freetime At POD', value: record['freeTime'] || '' },
        { name: 'note', label: 'Note', value: record['note'] || '' },
      ];

      // Create header row with borders
      const headerRow = fields.map(f => f.label).join('\t');

      // Create value row with borders
      const valueRow = fields.map(f => {
        return f.value
      }).join('\t');

      // Combine with special characters for Excel-like borders
      const tableData = `${headerRow}\n${valueRow}`;

      // Copy to clipboard with HTML format for Excel
      const htmlData = `
        <table border="1" style="border-collapse: collapse">
          <tr>${fields.map(f => `<td style="border: 1px solid #000; padding: 4px">${f.label}</td>`).join('')}</tr>
          <tr>${fields.map(f => `<td style="border: 1px solid #000; padding: 4px">${f.value}</td>`).join('')}</tr>
        </table>
      `;

      // Create a temporary element to hold the HTML data
      const tempElement = document.createElement('div');
      tempElement.innerHTML = htmlData;

      // Copy to clipboard with HTML format
      const clipboardData = new ClipboardItem({
        'text/plain': new Blob([tableData], { type: 'text/plain' }),
        'text/html': new Blob([tempElement.innerHTML], { type: 'text/html' })
      });

      navigator.clipboard.write([clipboardData]).then(() => {
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      });
    };

    const fieldBuilder = new FieldBuilder(tooltipFields)
      .addBasicFields()
      .addCustomFields([
        {
          name: '_index_', label: '#', width: 40, container: 'fixed-left',
          removable: false, resizable: false,
          customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, record: grid.DisplayRecord, _focus: boolean) => {
            return (
              <bs.CssTooltip width={200} position='bottom-right' offset={{ x: 200, y: 0 }}>
                <bs.CssTooltipToggle>
                  <div className='flex-hbox justify-content-center align-items-center' onClick={() => handleClick(_ctx, _field, record)}>
                    {record.getDisplayRow()}
                    {/* <FeatherIcon.BarChart2 size={12} /> */}
                  </div>
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent className="d-flex flex-column rounded" >
                  <div className='rounded text-secondary fw-normal'>Click để copy theo dạng lưới.</div>
                </bs.CssTooltipContent>
              </bs.CssTooltip>
            )
          }
        },
      ])
      .addLocationFields(['Port'])
      .addValidDateField()
      .addCarrierField(uiList, 'Carrier', 'fixed-left')
      .addCurrencyField()
      .addPriceField('dry20Price', '20DC')
      .addPriceField('dry40Price', '40DC')
      .addPriceField('highCube40Price', '40HC')
      .addCustomFields([
        {
          name: 'surchargeNote', label: T(`Surcharge`), width: 180,
          editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
            return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
          }
        },
        {
          name: 'addCharge:TLX:SHIPMENT', label: T(`Telex /SWB`), width: 150,
          fieldDataGetter: (record: any) => {
            let name = "Telex charge/SWB/MBL"
            let addCharges: Array<any> = record['additionalCharges'] || []
            let match = addCharges.findLast(sel => sel.label === 'B_TLX');
            if (match) {
              return (match['unitPrice'] || '') + ' ' + match['note']
            }
            return '';
          },
          editor: {
            type: 'currency', enable: false, onInputChange: GridConfigFactory.onInputChange,
            renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
              const { displayRecord, tabIndex } = ctx;
              let record = displayRecord.record;
              let val: any = 0;
              let name = "addCharge:TLX:SHIPMENT"

              if (record[name] && record[name] > 0) {
                val = record[name];
              } else {
                let addCharges: Array<any> = record['additionalCharges'] || []
                let match = addCharges.findLast(sel => sel.label === 'B_TLX');
                if (match) {
                  let val = match['finalCharge'] || 0;
                  if (val === 0 && match['note']) {
                    val = match['note'];
                  }
                  record[name] = val
                }
              }

              return (
                <input.BBCurrencyField
                  bean={record}
                  precision={3}
                  style={{ height: record['rowHeight'] || CELL_HEIGHT }}
                  field={ctx.fieldConfig.name}
                  tabIndex={tabIndex}
                  onInputChange={onInputChange}
                />
              );
            },
          },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
            return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
          }
        }
      ])
      .addAgentField(uiList)
      .addCustomFields([
        {
          name: 'cutoff', label: T(`Cut off time`), width: 120,
          editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
            return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
          }
        },
        {
          name: 'etd', label: T(`ETD`), width: 120,
          editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
            return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
          }
        },
      ])
      .addTransitTimeField()
      .addTransitPortField()
      .addCustomFields([
        {
          name: 'freeTime', label: T('Freetime At POD'), width: 200,
          editor: { type: "string", onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
            return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
          }
        },
        UIPriceListConfigUtil.FIELD_NOTES(uiList),
        {
          name: 'createdBy', label: T('Creator.'), width: 120, filterable: true,
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },

        {
          name: 'createdTime', label: T('Created Time'), width: 170,
          filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime
        },
        {
          name: 'modifiedTime', label: T('Modified Time'), width: 170,
          filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime
        },
      ]);

    fieldBuilder.addGroup('_OF', {
      label: 'O/F',
      fields: ['dry20Price', 'dry40Price', 'highCube40Price']
    })

    return {
      fields: fieldBuilder.build(),
      fieldGroups: undefined,
    };
  }

  static LCL_EXPORT_CONFIG(uiList: entity.DbEntityList): PriceListConfig {

    const customAddChargeField = {
      name: 'addCharge:B_AFR:SET', label: T(`AFR/AMS/ISF`), width: 120, sortable: true,
      editor: {
        type: 'currency',
        onInputChange: this.onInputChange,
        renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
          const { displayRecord, tabIndex } = ctx;
          let record = displayRecord.record;
          let val: any = 0;
          let name = "addCharge:B_AFR:SET"

          if (record[name] && record[name] > 0) {
            val = record[name];
          } else {
            let addCharges: Array<any> = record['additionalCharges'] || []
            let match = addCharges.findLast(sel => sel.label === 'B_AFR');
            if (match) {
              let val = match['finalCharge'] || 0;
              if (val === 0 && match['note']) {
                val = match['note'];
              }
              record[name] = val
            }
          }

          return (
            <input.BBCurrencyField bean={record} precision={3}
              style={{ height: record['rowHeight'] || CELL_HEIGHT }}
              field={ctx.fieldConfig.name} tabIndex={tabIndex} onInputChange={onInputChange} />
          );
        },
      },
      fieldDataGetter: (record: any) => {
        if (record['addCharge:B_AFR:SET']) return record['addCharge:B_AFR:SET']
        let chargeName = "B_AFR"
        let addCharges: Array<any> = record['additionalCharges'] || []
        let match = addCharges.findLast(sel => sel.label === chargeName);
        if (match) {
          record['addCharge:B_AFR:SET'] = match['finalCharge']
          return match['finalCharge']
        }
        return 0;
      },
      customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
        const record = dRecord.record;
        if (record['addCharge:B_AFR:SET']) {
          return (<div className="text-wrap position-relative" >{record['addCharge:B_AFR:SET']}</div>);
        }

        const addChargeSplits = field.name.split(':');
        let chargeName = addChargeSplits[1];
        let addCharges: Array<any> = record['additionalCharges'] || []
        let match = addCharges.findLast(sel => sel.label === chargeName);
        if (match) {
          record['addCharge:B_AFR:SET'] = match['finalCharge']
          return (<div className="text-wrap text-end position-relative" > {match['finalCharge']}</div>);
        }
        return (<div className="text-wrap text-end position-relative" >{'-'}</div>);
      }
    }

    let tooltipFields: TooltipField[] = [
      { key: 'localChargeAtDest', label: 'LCC Dest' },
      { key: 'note', label: 'Note' },
    ]

    const fieldBuilder = new FieldBuilder(tooltipFields)
      .addBasicFields()
      .addLocationFields(['Port'])
      .addCurrencyField()
      .addPriceField('freightChargeLCL', "Freight (CBM)")
      .addLocalChargeField('CFS', 'CBM')
      .addLocalChargeField('THC', 'CBM')
      .addLocalChargeField('EBS', 'CBM')
      .addLocalChargeField('LSS', 'CBM')
      .addLocalChargeField('RR', 'CBM')
      .addLocalChargeField('GRI', 'CBM')
      .addLocalChargeField('DDC', 'CBM')
      .addCustomFields([customAddChargeField])
      .addLocalChargeField('BILL', 'SET')
      .addFrequencyField()
      .addTransitTimeField()
      .addTransitPortField()
      .addCustomFields([{
        name: 'stuffingNote', label: T(`Stuffing`), width: 150,
        editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
          return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
        }
      }])
      .addValidDateField()
      .addCustomFields([
        UIPriceListConfigUtil.FIELD_NOTES(uiList),
        {
          name: 'localChargeAtDest', label: T(`LCC Dest`), width: 350,
          editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
            return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
          }
        },
        {
          name: 'createdBy', label: T('Creator.'), width: 120, filterable: true,
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
            return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
          }
        },
        // UIPriceListConfigUtil.FIELD_EMPLOYEE({ name: "assigneeLabel", label: 'Creator.' }, 'assigneeEmployeeId'),
        {
          name: 'createdTime', label: T('Created Time'), width: 170,
          filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime
        },
        {
          name: 'modifiedTime', label: T('Modified Time'), width: 170,
          filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime
        },
      ])

    fieldBuilder.addGroup('localCharge', {
      label: T('Local Charge'), visible: true,
      fields: [
        'addCharge:CFS:CBM', 'addCharge:THC:CBM', 'addCharge:EBS:CBM', 'addCharge:LSS:CBM', 'addCharge:RR:CBM',
        'addCharge:GRI:CBM', 'addCharge:DDC:CBM', 'addCharge:AFR_AMS_ISD:CBM', 'addCharge:BILL:CBM'
      ]
    })

    return {
      fields: fieldBuilder.build(),
      fieldGroups: undefined,
    };
  }

  static LCL_IMPORT_CONFIG(uiList: entity.DbEntityList): PriceListConfig {

    let customFields: grid.FieldConfig[] = [
      {
        name: 'surchargeNote', label: T(`Surcharge`), width: 180,
        editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
          return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
        }
      },
      {
        name: 'cutoff', label: T(`Cut off time`), width: 180,
        editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
          return FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields)
        }
      },
    ];

    let tooltipFields: TooltipField[] = [
      { key: 'handlingAgentPartnerLabel', label: 'Agent' },
      { key: 'note', label: 'Note' },
    ]

    const fieldBuilder = new FieldBuilder(tooltipFields)
      .addBasicFields()
      .addLocationFields(['Port'])
      .addCurrencyField()
      .addPriceField('minimumChargeLCL', "< 1 CBM")
      .addPriceField('less2CbmPrice', "< 2 CBM")
      .addPriceField('less3CbmPrice', "< 3 CBM")
      .addPriceField('less5CbmPrice', "< 5 CBM")
      .addPriceField('less7CbmPrice', "< 7 CBM")
      .addPriceField('less10CbmPrice', "< 10 CBM")
      .addPriceField('geq10CbmPrice', "< 15 CBM")
      .addLocalChargeField('DO', 'SET')
      .addLocalChargeField('CFS', 'CBM')
      .addLocalChargeField('THC', 'CBM')
      .addLocalChargeField('CIC', 'CBM')
      .addCustomFields(customFields)
      .addFrequencyField()
      .addTransitTimeField()
      .addTransitPortField()
      .addValidDateField()
      .addAgentField(uiList, "Agent")
      .addCustomFields([
        UIPriceListConfigUtil.FIELD_NOTES(uiList),
        { name: 'createdBy', label: T('Creator.'), width: 120, filterable: true },
      ])

    fieldBuilder.addGroup('_OF', {
      label: T('Freight (CBM)'), visible: true,
      fields: ['minimumChargeLCL', 'less2CbmPrice', 'less3CbmPrice', 'less5CbmPrice', 'less7CbmPrice', 'less10CbmPrice', 'geq10CbmPrice']
    })

    fieldBuilder.addGroup('localCharge', {
      label: T('Local Charge'), visible: true,
      fields: ['addCharge:DO:SET', 'addCharge:CFS:RT', 'addCharge:THC:RT', 'addCharge:CIC:RT']
    })

    return {
      fields: fieldBuilder.build(),
      fieldGroups: undefined,
    };
  }

  static TRUCK_CONFIG(uiList: entity.DbEntityList, isCBT: boolean = false): PriceListConfig {

    let tooltipFields: TooltipField[] = [
      { key: 'pickupAddress', label: 'Pickup Address' },
      { key: 'deliveryAddress', label: 'Delivery Address' },
      { key: 'note', label: 'Note' },
    ]

    const fieldBuilder = new FieldBuilder(tooltipFields)
      .addBasicFields()
      .addTruckLocationFields()
      .addCurrencyField()
      .addCustomFields([
        {
          name: 'km2way', label: T(`Km2Way`), width: 100,
          editor: { type: 'double', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
        {
          name: 'oilPriceInEffect', label: T(`Oil Price`),
          editor: { type: 'double', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
        {
          name: 'transitBorder', label: T(`Transit Border`), width: 180, state: { visible: isCBT },
          editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
      ])
      .addCurrencyPriceField('truck1Ton25Price', '1.25T')
      .addCurrencyPriceField('truck1Ton5Price', '1.5T')
      .addCurrencyPriceField('truck2Ton5Price', '2.5T')
      .addCurrencyPriceField('truck3Ton5Price', '3.5T')
      .addCurrencyPriceField('truck5TonPrice', '5T')
      .addCurrencyPriceField('truck7TonPrice', '7T')
      .addCurrencyPriceField('truck8TonPrice', '8T')
      .addCurrencyPriceField('truck9TonPrice', '9T')
      .addCurrencyPriceField('truck10TonPrice', '10T')
      .addCurrencyPriceField('truck12TonPrice', '12T')
      .addCurrencyPriceField('truck15TonPrice', '15T')
      .addCurrencyPriceField('truck30TonPrice', '30T')
      .addCustomFields([
        {
          name: "stackableUSDPerKG", label: "USD/Kg (Stackable)", format: (val: any) => formater.currency(val, 2),
          state: { visible: isCBT }, sortable: true, width: 120,
          editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange }
        },
        {
          name: "nonstackableUSDPerKG", label: "USD/Kg (Nonstackable)", format: (val: any) => formater.currency(val, 2),
          state: { visible: isCBT }, sortable: true, width: 120,
          editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        },
        {
          name: "customFeeAtLangSon", label: "At Lạng Sơn", format: (val: any) => formater.currency(val, 2),
          state: { visible: isCBT }, sortable: true, width: 120,
          editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange }
        },
        {
          name: "customFeeAtPinXiang", label: "At Pin Xiang", format: (val: any) => formater.currency(val, 2),
          state: { visible: isCBT }, sortable: true, width: 120,
          editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange }
        },
        {
          name: "frequency", label: "Frequency",
          state: { visible: isCBT }, sortable: true, width: 120,
          editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
        },
        {
          name: "transitTime", label: "Transit Time",
          state: { visible: isCBT }, sortable: true, width: 120,
          editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
        },
      ])
      .addCurrencyField()
      .addValidDateField()
      .addCustomFields([
        UIPriceListConfigUtil.FIELD_NOTES(uiList),
        {
          name: "carrierLabel", label: T("Subcontractor"), width: 250, state: { visible: !isCBT },
          editor: {
            type: "string",
            renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
              const { displayRecord, gridContext, focus, tabIndex } = ctx;
              let uiList = gridContext.uiRoot as entity.DbEntityList;
              const { appContext, pageContext } = uiList.props;
              let record = displayRecord.record;

              return (
                <BBRefSubContactor placeholder='Sub-contractor/ Carrier' tabIndex={tabIndex} autofocus={focus} hideMoreInfo
                  appContext={appContext} pageContext={pageContext} minWidth={350}
                  bean={record} beanIdField={'carrierPartnerId'} beanLabelField={'carrierLabel'}
                  onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, _userInput: string) => onInputChange(bean, 'carrierPartnerId', null, selectOpt)} />
              )
            },
            onInputChange: GridConfigFactory.onInputChange
          },
          customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
            let record = dRecord.record;
            let uiList = ctx.uiRoot as entity.DbEntityList
            const { appContext, pageContext } = uiList.props;
            return (
              <BBRefSubContactor placeholder='Sub-contractor/ Carrier' hideMoreInfo
                appContext={appContext} pageContext={pageContext} minWidth={350}
                bean={record} beanIdField={'carrierPartnerId'} beanLabelField={'carrierLabel'} />
            )
          }
        },
        {
          name: 'targetReferenceLabel', label: 'Partner Reference', width: 250, state: { visible: !isCBT },
          editor: {
            type: "string",
            renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
              const { displayRecord, gridContext, focus, tabIndex } = ctx;
              let uiList = gridContext.uiRoot as entity.DbEntityList;
              const { appContext, pageContext } = uiList.props;
              let record = displayRecord.record;
              return (
                <BBRefBFSOnePartner placeholder='Customer' tabIndex={tabIndex} autofocus={focus} allowUserInput
                  appContext={appContext} pageContext={pageContext} minWidth={350} hideMoreInfo
                  bean={record} beanIdField={'targetReferenceId'} beanLabelField={'targetReferenceLabel'} partnerType='Customer'
                  onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, _userInput: string) => onInputChange(bean, 'targetReferenceId', null, selectOpt)} />
              )
            },
            onInputChange: GridConfigFactory.onInputChange
          },
        },
        { name: 'createdBy', label: T('Creator.'), width: 120, filterable: true },
        {
          name: 'modifiedTime', label: T('Modified Time'), width: 170,
          filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime
        },
      ]);

    return {
      fields: fieldBuilder.build(),
      fieldGroups: undefined
    };
  }

  static CONTAINER_CONFIG(uiList: entity.DbEntityList, isCBT: boolean = false): PriceListConfig {

    let containerPriceFields: grid.FieldConfig[] = [
      {
        name: "contDry20Lt10TonPrice", label: "< 10T",
        hint: 'DC', width: 100,
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contDry20Geq10TonPrice", label: "≤ 21T",
        hint: 'DC', width: 100,
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customHeaderRender: (ctx: grid.VGridContext, field: grid.FieldConfig, headerEle: any) => {
          return (
            <div className='flex-hbox align-items-center'>
              <span className='text-secondary'>≤</span> &nbsp;21T
            </div>
          )
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contDry20Geq17TonPrice", label: "> 21T",
        hint: 'DC', width: 100,
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customHeaderRender: (ctx: grid.VGridContext, field: grid.FieldConfig, headerEle: any) => {
          return (
            <div className='flex-hbox align-items-center'>
              <span className='text-secondary'>&gt;</span> &nbsp;21T
            </div>
          )
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contDry40Lt17TonPrice", label: "≤ 18T",
        hint: 'Dry container (DC)', width: 120,
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customHeaderRender: (ctx: grid.VGridContext, field: grid.FieldConfig, headerEle: any) => {
          return (
            <div className='flex-hbox align-items-center'>
              <span className='text-secondary'>≤</span> &nbsp;18T
            </div>
          )
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contDry40Geq17TonPrice", label: "> 18T",
        hint: 'Dry container (DC)', width: 120,
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customHeaderRender: (ctx: grid.VGridContext, field: grid.FieldConfig, headerEle: any) => {
          return (
            <div className='flex-hbox align-items-center'>
              <span className='text-secondary'>&gt;</span> &nbsp;18T
            </div>
          )
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contReefer20Lt17TonPrice", label: "< 17T", sortable: true, state: { visible: false },
        hint: 'Reefer container (RF)',
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contReefer20Geq17TonPrice", label: ">= 17T", sortable: true, state: { visible: false },
        hint: 'Reefer container (RF)',
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customHeaderRender: (ctx: grid.VGridContext, field: grid.FieldConfig, headerEle: any) => {
          return (
            <div className='flex-hbox align-items-center'>
              <span className='text-secondary'>≥</span> &nbsp;17T
            </div>
          )
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contReefer40Lt17TonPrice", label: "< 17T", sortable: true, hint: 'Reefer container (RF)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contReefer40Geq17TonPrice", label: ">= 17T", sortable: true, hint: 'Reefer container (RF)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contOpenTop20Lt17TonPrice", label: "< 17T", sortable: true, hint: 'Open Top Container (RF)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contOpenTop20Geq17TonPrice", label: ">= 17T", sortable: true, hint: 'Open Top Container (RF)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contOpenTop40Lt17TonPrice", label: "< 17T", sortable: true, hint: 'Open Top Container (RF)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contOpenTop40Geq17TonPrice", label: ">= 17T", sortable: true, hint: 'Open Top Container (RF)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contFlatRack20Lt17TonPrice", label: "< 17T", sortable: true, hint: 'Flat rack Container (FR)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contFlatRack20Geq17TonPrice", label: ">= 17T", sortable: true, hint: 'Flat rack Container (FR)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contFlatRack40Lt17TonPrice", label: "< 17T", sortable: true, hint: 'Flat rack Container (FR)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contFlatRack40Geq17TonPrice", label: ">= 17T", sortable: true, hint: 'Flat rack Container (FR)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contTank20Lt17TonPrice", label: "< 17T", sortable: true, hint: 'Tank Container (Tank)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contTank20Geq17TonPrice", label: ">= 17T", sortable: true, hint: 'Tank Container (Tank)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contTank40Lt17TonPrice", label: "< 17T", sortable: true, hint: 'Tank Container (Tank)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
      {
        name: "contTank40Geq17TonPrice", label: ">= 17T", sortable: true, hint: 'Tank Container (Tank)',
        state: { visible: false },
        editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
      },
    ]

    if (isCBT) {
      containerPriceFields = [
        {
          name: "contHighCube40Price", label: "40HC", sortable: true,
          editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
        {
          name: "contHighCube45Price", label: "45HC", sortable: true,
          editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
        {
          name: "contReefer40Price", label: "40RF", sortable: true,
          editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
        {
          name: "contReefer45Price", label: "45RF", sortable: true,
          editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
        {
          name: "customFeeAtLangSon", label: "At Lạng Sơn", sortable: true,
          editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
        {
          name: "customFeeAtPinXiang", label: "At Pin Xiang", sortable: true,
          editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
      ]
    }

    let tooltipFields: TooltipField[] = [
      { key: 'pickupAddress', label: 'Pickup Address' },
      { key: 'deliveryAddress', label: 'Delivery Address' },
      { key: 'carrierLabel', label: 'Subcontractor' },
      { key: 'note', label: 'Note' },
      { key: 'targetReferenceLabel', label: 'Priority Price For Customers' },
    ]

    const fieldBuilder = new FieldBuilder(tooltipFields)
      .addBasicFields()
      .addTruckLocationFields()
      .addCurrencyField()
      .addCustomFields([
        {
          name: 'transitBorder', label: T(`Transit Border`), width: 180,
          state: { visible: isCBT },
          editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
        {
          name: 'km2way', label: T(`Km2Way`), width: 100,
          editor: { type: 'double', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
        {
          name: 'oilPriceInEffect', label: T(`Oil Price`),
          editor: { type: 'currency', onInputChange: GridConfigFactory.onInputChange },
          customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => FieldBuilder.renderTooltipAdvanced(_ctx, field, dRecord, tooltipFields),
        },
        ...containerPriceFields
      ])
      .addCurrencyField()
      .addValidDateField()
      .addCustomFields([
        {
          name: "frequency", label: "Frequency", state: { visible: isCBT },
          editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange }
        },
        {
          name: "transitTime", label: "Transit Time", state: { visible: isCBT },
          editor: { type: 'string', onInputChange: GridConfigFactory.onInputChange }
        },
        UIPriceListConfigUtil.FIELD_NOTES(uiList),
        {
          name: "carrierLabel", label: T("Subcontractor"), width: 250, state: { visible: !isCBT },
          editor: {
            type: "string",
            renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
              const { displayRecord, gridContext, focus, tabIndex } = ctx;
              let uiList = gridContext.uiRoot as entity.DbEntityList;
              const { appContext, pageContext } = uiList.props;
              let record = displayRecord.record;
              return (
                <BBRefSubContactor placeholder='Sub-contractor/ Carrier' tabIndex={tabIndex} autofocus={focus} hideMoreInfo
                  appContext={appContext} pageContext={pageContext} minWidth={350} style={{ minHeight: record['rowHeight'] || 45 }}
                  bean={record} beanIdField={'carrierPartnerId'} beanLabelField={'carrierLabel'}
                  onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, _userInput: string) => onInputChange(bean, 'carrierPartnerId', null, selectOpt)} />
              )
            },
            onInputChange: GridConfigFactory.onInputChange
          },
          customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
            let record = dRecord.record;
            let uiList = ctx.uiRoot as entity.DbEntityList
            const { appContext, pageContext } = uiList.props;
            return (
              <BBRefSubContactor placeholder='Sub-contractor/ Carrier' style={{ minHeight: record['rowHeight'] || 45 }}
                appContext={appContext} pageContext={pageContext} minWidth={350} hideMoreInfo
                bean={record} beanIdField={'carrierPartnerId'} beanLabelField={'carrierLabel'} />
            )
          }
        },
        {
          name: 'targetReferenceLabel', label: 'Partner Reference', width: 250, state: { visible: !isCBT },
          editor: {
            type: "string",
            renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
              const { displayRecord, gridContext, focus, tabIndex } = ctx;
              let uiList = gridContext.uiRoot as entity.DbEntityList;
              const { appContext, pageContext } = uiList.props;
              let record = displayRecord.record;
              return (
                <BBRefBFSOnePartner placeholder='Customer' tabIndex={tabIndex} autofocus={focus} allowUserInput hideMoreInfo
                  appContext={appContext} pageContext={pageContext} minWidth={350} style={{ minHeight: record['rowHeight'] || 45 }}
                  bean={record} beanIdField={'targetReferenceId'} beanLabelField={'targetReferenceLabel'} partnerType='Customer'
                  onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, _userInput: string) => onInputChange(bean, 'targetReferenceId', null, selectOpt)} />
              )
            },
            onInputChange: GridConfigFactory.onInputChange
          },
        },
        { name: 'createdBy', label: T('Creator.'), width: 120, filterable: true },
        {
          name: 'modifiedTime', label: T('Modified Time'), width: 170,
          filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime
        },
      ]);

    return {
      fields: fieldBuilder.build(),
      fieldGroups: this.createContainerFieldGroups(isCBT),
    };
  }

  private static createContainerFieldGroups(isCBT: boolean = false): Record<string, grid.FieldGroup> {
    let fieldGroup: Record<string, grid.FieldGroup> = {
      pickUp: {
        label: T('Origin'), visible: true,
        fields: ['pickupLocationCode', 'pickupAddress']
      },
      deliveryTo: {
        label: T('Destination'), visible: true,
        fields: ['deliveryLocationCode', 'deliveryAddress']
      },
      _20FeetDC: {
        label: T("20'"),
        fields: ["contDry20Lt10TonPrice", "contDry20Geq10TonPrice", "contDry20Geq17TonPrice"]
      },
      _40FeetDC: {
        label: T("40'"),
        fields: ["contDry40Lt17TonPrice", "contDry40Geq17TonPrice"]
      },

      _20FeetRF: {
        label: T("20RF"),
        fields: ["contReefer20Lt17TonPrice", "contReefer20Geq17TonPrice"]
      },
      _20FeetRFO: {
        label: T("20OT"),
        fields: ["contOpenTop20Lt17TonPrice", "contOpenTop20Geq17TonPrice"]
      },
      _20FeetFR: {
        label: T("20FR"),
        fields: ["contFlatRack20Lt17TonPrice", "contFlatRack20Geq17TonPrice"]
      },
      _20FeetT: {
        label: T("20Tank"),
        fields: ["contTank20Lt17TonPrice", "contTank20Geq17TonPrice"]
      },
      _40FeetRF: {
        label: T("40RF"),
        fields: ["contReefer40Lt17TonPrice", "contReefer40Geq17TonPrice"]
      },
      _40FeetRFO: {
        label: T("40OT"),
        fields: ["contOpenTop40Lt17TonPrice", "contOpenTop40Geq17TonPrice"]
      },
      _40FeetFR: {
        label: T("40FR"),
        fields: ["contFlatRack40Lt17TonPrice", "contFlatRack40Geq17TonPrice"]
      },
      _40FeetT: {
        label: T("40Tank"),
        fields: ["contTank40Lt17TonPrice", "contTank40Geq17TonPrice"]
      },
    };
    return fieldGroup;
  }

  private static createTruckFieldGroups(isCBT: boolean = false): Record<string, grid.FieldGroup> {
    let fieldGroup: Record<string, grid.FieldGroup> = {
      pickUp: {
        label: T('Origin'), visible: true,
        fields: ['pickupLocationCode', 'pickupAddress']
      },
      deliveryTo: {
        label: T('Destination'), visible: true,
        fields: ['deliveryLocationCode', 'deliveryAddress']
      },
      truck: {
        label: T("Truck (Trip)"),
        fields: [
          'truck1Ton25Price', 'truck1Ton5Price', 'truck2Ton5Price', 'truck3Ton5Price',
          'truck5TonPrice', 'truck7TonPrice', 'truck8TonPrice', 'truck9TonPrice',
          'truck10TonPrice', 'truck12TonPrice', 'truck15TonPrice', 'truck30TonPrice'
        ]
      },
      validate: {
        label: T("Validate"), visible: true,
        fields: ['validFrom', 'validTo']
      },
      CustomFee: {
        label: T("Custom Fee"), visible: isCBT,
        fields: ["customFeeAtLangSon", "customFeeAtPinXiang"]
      },
    };
    return fieldGroup;
  }


}
