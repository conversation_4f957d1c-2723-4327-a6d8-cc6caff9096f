import React from "react";
import * as icon from 'react-feather';
import { app, bs } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";

import { T } from "./backend";
import { DocumentSetList, DocumentSetListPlugin } from "./DocumentSetList";
import { DocumentList, DocumentListPlugin } from "./DocumentList";
import { UICustomIEApiPlugin } from "./api/CustomIEApiPlugin";
import { DocumentSetTagList, DocumentSetTagListPlugin } from "./DocumentSetTagList";
import space = app.space;

class DocumentSpacePlugin extends space.SpacePlugin {
  constructor() {
    super('document', 'Document Navigation');
  }

  createUserScreens(): app.space.ScreenConfig[] {
    let company = app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl();
    let companyCode = company.companyCode;
    let configs: space.ScreenConfig[] = [
      {
        id: "document-ie", label: T("Document IE"), icon: icon.Folder,
        checkPermission: {
          feature: { module: 'user', name: 'my-employee-space' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let config: bs.TabPaneConfig = {
            tabs: [
              {
                name: 'document-set', label: 'Document Set', active: true, Icon: icon.Folder,
                renderContent: (_ctx: bs.UIContext) => {
                  return (
                    <DocumentSetList appContext={appCtx} pageContext={pageCtx} storage={new module.storage.CompanyStorage(companyCode)}
                      plugin={new DocumentSetListPlugin().withScope(app.AppDataScope.OWNER.scope)} />
                  );
                }
              },
              {
                name: 'documents', label: 'Documents', Icon: icon.FileText,
                renderContent: (_ctx: bs.UIContext) => {
                  return (
                    <DocumentList
                      appContext={appCtx} pageContext={pageCtx} viewType="DocumentSet" viewName="aggregation"
                      storage={new module.storage.CompanyStorage(companyCode)} plugin={new DocumentListPlugin().withScope(app.AppDataScope.OWNER.scope)} />
                  );
                }
              },
            ]
          };
          let html = (<bs.DefaultTabPane config={config} />);
          return html;
        },
        screens: [
        ]
      }
    ]
    return configs;
  }
  createCompanyScreens(): app.space.ScreenConfig[] {
    let company = app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl();
    let companyCode = company.companyCode;
    let configs: space.ScreenConfig[] = [
      {
        id: "document-ie", label: T("Document IE"), icon: icon.Folder,
        checkPermission: {
          feature: { module: 'document', name: 'company-document' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let config: bs.TabPaneConfig = {
            tabs: [
              {
                name: 'document-set', label: 'Document Set', active: true, Icon: icon.Folder,
                renderContent: (_ctx: bs.UIContext) => {
                  return (
                    <DocumentSetList
                      appContext={appCtx} pageContext={pageCtx}
                      storage={new module.storage.CompanyStorage(companyCode)} plugin={new DocumentSetListPlugin()} />
                  );
                }
              },
              {
                name: 'documents', label: 'Documents', Icon: icon.FileText,
                renderContent: (_ctx: bs.UIContext) => {
                  return (
                    <DocumentList key={'documents'}
                      appContext={appCtx} pageContext={pageCtx} viewType="DocumentSet" viewName="aggregation"
                      storage={new module.storage.CompanyStorage(companyCode)} plugin={new DocumentListPlugin()} />
                  );
                }
              },
              {
                name: "document-set-tag", label: T("Document Set Tag"),

                renderContent: (_ctx: bs.UIContext) => {
                  return (
                    <DocumentSetTagList appContext={appCtx} pageContext={pageCtx} type={'page'}
                      plugin={new DocumentSetTagListPlugin()} />
                  );
                }
              },
            ]
          };
          let html = (<bs.DefaultTabPane config={config} />);
          return html;
        },
        screens: [
        ]
      }
    ]
    return configs;
  }

}

export function init() {
  space.SpacePluginManager.register(new DocumentSpacePlugin());
  module.security.UIApiPluginManager.register(new UICustomIEApiPlugin());
}